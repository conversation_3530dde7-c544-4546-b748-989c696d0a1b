[{"elementId": "1000", "listImage": "list_car_n", "name": "diagram_element_scene_car", "nomalImage": "scene_car_n", "selectedImage": "scene_car_p", "statisticsImage": "statistics_car_n", "type": 0}, {"elementId": "1001", "listImage": "list_road_n", "name": "diagram_element_scene_road", "nomalImage": "scene_road_n", "selectedImage": "scene_road_p", "statisticsImage": "statistics_road_n", "type": 0}, {"elementId": "1003", "listImage": "list_stairs_n", "name": "diagram_element_scene_stairs", "nomalImage": "scene_stairs_n", "selectedImage": "scene_stairs_p", "statisticsImage": "statistics_stairs_n", "type": 0}, {"elementId": "1004", "listImage": "list_openwindow_n", "name": "diagram_element_scene_opened_windows", "nomalImage": "scene_openwindow_n", "selectedImage": "scene_openwindow_p", "statisticsImage": "statistics_openwindow_n", "type": 0}, {"elementId": "1005", "listImage": "list_closewindow_n", "name": "diagram_element_scene_closed_windows", "nomalImage": "scene_closewindow_n", "selectedImage": "scene_closewindow_p", "statisticsImage": "statistics_closewindow_n", "type": 0}, {"elementId": "1006", "listImage": "list_opendoor_n", "name": "diagram_element_scene_opened_door", "nomalImage": "scene_opendoor_n", "selectedImage": "scene_opendoor_p", "statisticsImage": "statistics_opendoor_n", "type": 0}, {"elementId": "1007", "listImage": "list_closedoor_n", "name": "diagram_element_scene_closed_door", "nomalImage": "scene_closedoor_n", "selectedImage": "scene_closedoor_p", "statisticsImage": "statistics_closedoor_n", "type": 0}, {"elementId": "1008", "listImage": "list_octagon_n", "name": "diagram_element_scene_octagon", "nomalImage": "scene_octagon_n", "selectedImage": "scene_octagon_p", "statisticsImage": "statistics_sceoctagon_n", "type": 0}, {"elementId": "1009", "listImage": "list_rectangle_n", "name": "diagram_element_scene_rectangle", "nomalImage": "scene_rectangle_n", "selectedImage": "scene_rectangle_p", "statisticsImage": "statistics_scerectangle_n", "type": 0}, {"elementId": "1010", "listImage": "list_square_n", "name": "diagram_element_scene_square", "nomalImage": "scene_square_n", "selectedImage": "scene_square_p", "statisticsImage": "statistics_scesquare_n", "type": 0}, {"elementId": "1011", "listImage": "list_circle_n", "name": "diagram_element_scene_circle", "nomalImage": "scene_circle_n", "selectedImage": "scene_circle_p", "statisticsImage": "statistics_scecircle_n", "type": 0}, {"elementId": "1012", "listImage": "list_triangle_n", "name": "diagram_element_scene_triagle", "nomalImage": "scene_triangle_n", "selectedImage": "scene_triangle_p", "statisticsImage": "statistics_scetriangle_n", "type": 0}, {"elementId": "1013", "listImage": "list_pentagram_n", "name": "diagram_element_scene_pentagram", "nomalImage": "scene_pentagram_n", "selectedImage": "scene_pentagram_p", "statisticsImage": "statistics_scepentagram_n", "type": 0}, {"elementId": "1014", "listImage": "list_rhombus_n", "name": "diagram_element_scene_rhombus", "nomalImage": "scene_rhombus_n", "selectedImage": "scene_rhombus_p", "statisticsImage": "statistics_scerhombus_n", "type": 0}, {"elementId": "1015", "listImage": "list_pentagon_n", "name": "diagram_element_scene_pentagon", "nomalImage": "scene_pentagon_n", "selectedImage": "scene_pentagon_p", "statisticsImage": "statistics_scepentagon_n", "type": 0}, {"elementId": "1016", "listImage": "list_wavy_line", "name": "diagram_element_scene_wavy_line", "nomalImage": "scene_coastline_n", "selectedImage": "scene_coastline_p", "statisticsImage": "statistics_wavy_n", "type": 0}, {"elementId": "1017", "listImage": "list_doubleline_n", "name": "diagram_element_scene_double_line", "nomalImage": "scene_doubleline_n", "selectedImage": "scene_doubleline_p", "statisticsImage": "statistics_doubleline_n", "type": 0}, {"elementId": "1018", "listImage": "list_dottedline_n", "name": "diagram_element_scene_dotted_line", "nomalImage": "scene_dottedline_n", "selectedImage": "scene_dottedline_p", "statisticsImage": "statistics_dottedline_n", "type": 0}, {"elementId": "1019", "listImage": "list_typesomething_n", "name": "diagram_element_scene_line", "nomalImage": "scene_specialline_n", "selectedImage": "scene_specialline_p", "statisticsImage": "statistics_specialline_n", "type": 0}, {"elementId": "1020", "listImage": "list_curve_n", "name": "diagram_element_scene_curve", "nomalImage": "scene_curve_n", "selectedImage": "scene_curve_p", "statisticsImage": "statistics_curve_n", "type": 0}, {"elementId": "1021", "listImage": "list_moonlight_n", "name": "diagram_element_scene_moonlight", "nomalImage": "scene_moonlight_n", "selectedImage": "scene_moonlight_p", "statisticsImage": "statistics_moonlight_n", "type": 0}, {"elementId": "1022", "listImage": "list_sunlight_n", "name": "diagram_element_scene_sunlight", "nomalImage": "scene_sunlight_n", "selectedImage": "scene_sunlight_p", "statisticsImage": "statistics_sunlight_n", "type": 0}, {"elementId": "2000", "listImage": "list_table_n", "name": "diagram_element_prop_table", "nomalImage": "property_list_table_n", "selectedImage": "property_list_table_p", "statisticsImage": "statistics_table_n", "type": 1}, {"elementId": "2001", "listImage": "list_tablelamp_n", "name": "diagram_element_prop_table_lamp", "nomalImage": "property_tablelamp_n", "selectedImage": "property_tablelamp_p", "statisticsImage": "statistics_tablelamp_n", "type": 1}, {"elementId": "2002", "listImage": "list_chair_n", "name": "diagram_element_prop_chair", "nomalImage": "property_chair_n", "selectedImage": "property_chair_p", "statisticsImage": "statistics_chair_n", "type": 1}, {"elementId": "2003", "listImage": "list_3sofa_n", "name": "diagram_element_prop_sofa3", "nomalImage": "property_sofa03_n", "selectedImage": "property_sofa03_p", "statisticsImage": "statistics_sofa03_n", "type": 1}, {"elementId": "2004", "listImage": "list_2sofa_n", "name": "diagram_element_prop_sofa2", "nomalImage": "property_sofa02_n", "selectedImage": "property_sofa02_p", "statisticsImage": "statistics_sofa02_n", "type": 1}, {"elementId": "2005", "listImage": "list_sofa_n", "name": "diagram_element_prop_sofa1", "nomalImage": "property_sofa01_n", "selectedImage": "property_sofa01_p", "statisticsImage": "statistics_sofa01_n", "type": 1}, {"elementId": "2006", "listImage": "list_bed_n", "name": "diagram_element_prop_bed", "nomalImage": "property_bed_n", "selectedImage": "property_bed_p", "statisticsImage": "statistics_bed_n", "type": 1}, {"elementId": "2007", "listImage": "list_cabinet_n", "name": "diagram_element_prop_cabinet", "nomalImage": "property_cabinet_n", "selectedImage": "property_cabinet_p", "statisticsImage": "statistics_cabinet_n", "type": 1}, {"elementId": "2008", "listImage": "list_tv_n", "name": "diagram_element_prop_tv", "nomalImage": "property_tv_n", "selectedImage": "property_tv_p", "statisticsImage": "statistics_tv_n", "type": 1}, {"elementId": "2009", "listImage": "list_boom_n", "name": "diagram_element_prop_boom", "nomalImage": "property_boom_n", "selectedImage": "property_boom_p", "statisticsImage": "statistics_boom_n", "type": 1}, {"elementId": "2010", "listImage": "list_mic_n", "name": "diagram_element_prop_mic", "nomalImage": "property_mic_n", "selectedImage": "property_mic_p", "statisticsImage": "statistics_mic_n", "type": 1}, {"elementId": "2011", "listImage": "list_vase_n", "name": "diagram_element_prop_vase", "nomalImage": "property_vase_n", "selectedImage": "property_vase_p", "statisticsImage": "statistics_vase_n", "type": 1}, {"elementId": "2012", "listImage": "list_poctagon_n", "name": "diagram_element_prop_octagon", "nomalImage": "property_octagon_n", "selectedImage": "property_octagon_p", "statisticsImage": "statistics_propoctagon_n", "type": 1}, {"elementId": "2013", "listImage": "list_prectangle_n", "name": "diagram_element_prop_rectangle", "nomalImage": "property_rectangle_n", "selectedImage": "property_rectangle_p", "statisticsImage": "statistics_proprectangle_n", "type": 1}, {"elementId": "2014", "listImage": "list_psquare_n", "name": "diagram_element_prop_square", "nomalImage": "property_square_n", "selectedImage": "property_square_p", "statisticsImage": "statistics_propsquare_n", "type": 1}, {"elementId": "2015", "listImage": "list_pcircle_n", "name": "diagram_element_prop_circle", "nomalImage": "property_circle_n", "selectedImage": "property_circle_p", "statisticsImage": "statistics_propcircle_n", "type": 1}, {"elementId": "2016", "listImage": "list_ptriangle_n", "name": "diagram_element_prop_triagle", "nomalImage": "property_triangle_n", "selectedImage": "property_triangle_p", "statisticsImage": "statistics_proptriangle_n", "type": 1}, {"elementId": "2017", "listImage": "list_ppentagram_n", "name": "diagram_element_prop_pentagram", "nomalImage": "property_pentagram_n", "selectedImage": "property_pentagram_p", "statisticsImage": "statistics_proppentagram_n", "type": 1}, {"elementId": "2018", "listImage": "list_prhombus_n", "name": "diagram_element_prop_rhombus", "nomalImage": "property_rhombus_n", "selectedImage": "property_rhombus_p", "statisticsImage": "statistics_proprhombus_n", "type": 1}, {"elementId": "2019", "listImage": "list_ppentagon_n", "name": "diagram_element_prop_pentagon", "nomalImage": "property_pentagon_n", "selectedImage": "property_pentagon_p", "statisticsImage": "statistics_proppentagon_n", "type": 1}, {"elementId": "3000", "listImage": "list_lmale_n", "name": "diagram_element_character_male_lead", "nomalImage": "character_maleleading_n", "selectedImage": "character_maleleading_p", "statisticsImage": "statistics_maleleading_n", "type": 2}, {"elementId": "3001", "listImage": "list_lfemale_n", "name": "diagram_element_character_female_lead", "nomalImage": "character_femaleleading_n", "selectedImage": "character_femaleleading_p", "statisticsImage": "statistics_femaleleading_n", "type": 2}, {"elementId": "3002", "listImage": "list_lkids_n", "name": "diagram_element_character_child_lead", "nomalImage": "character_kidsleading_n", "selectedImage": "character_kidsleading_p", "statisticsImage": "statistics_kidsleading_n", "type": 2}, {"elementId": "3003", "listImage": "list_smale_n", "name": "diagram_element_character_male_support", "nomalImage": "character_malesupport_n", "selectedImage": "character_malesupport_p", "statisticsImage": "statistics_malesupport_n", "type": 2}, {"elementId": "3004", "listImage": "list_sfemale_n", "name": "diagram_element_character_female_support", "nomalImage": "character_femalesupport_n", "selectedImage": "character_femalesupport_p", "statisticsImage": "statistics_femalesupport_n", "type": 2}, {"elementId": "3005", "listImage": "list_skids_n", "name": "diagram_element_character_child_support", "nomalImage": "character_kidssupport_n", "selectedImage": "character_kidssupport_p", "statisticsImage": "statistics_kidssupport_n", "type": 2}, {"elementId": "3006", "listImage": "list_stunts_n", "name": "diagram_element_character_stunts", "nomalImage": "character_stunts_n", "selectedImage": "character_stunts_p", "statisticsImage": "statistics_stunts_n", "type": 2}, {"elementId": "3007", "listImage": "list_extras_n", "name": "diagram_element_character_extras", "nomalImage": "character_extras_n", "selectedImage": "character_extras_p", "statisticsImage": "statistics_extras_n", "type": 2}, {"elementId": "3008", "listImage": "list_crowdofextras_n", "name": "diagram_element_character_crowd_extras", "nomalImage": "character_crowdofextras_n", "selectedImage": "character_crowdofextras_p", "statisticsImage": "statistics_crowdofextras_n", "type": 2}, {"elementId": "4049", "listImage": "list_amaran100x_n", "name": "amaran 100x", "nomalImage": "alight_amaran_100x_n", "selectedImage": "alight_amaran_100x_p", "statisticsImage": "statistics_amaran100x_n", "type": 3, "combinationType": 14, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran 100 Series", "power": 130}, {"elementId": "4050", "listImage": "list_amaran100d_n", "name": "amaran 100d", "nomalImage": "alight_amaran_100d_n", "selectedImage": "alight_amaran_100d_p", "statisticsImage": "statistics_amaran100d_n", "type": 3, "combinationType": 14, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran 100 Series", "power": 130}, {"elementId": "4051", "listImage": "list_amaran200x_n", "name": "amaran 200x", "nomalImage": "alight_amaran_200x_n", "selectedImage": "alight_amaran_200x_p", "statisticsImage": "statistics_amaran200x_n", "type": 3, "combinationType": 14, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran 200 Series", "power": 250}, {"elementId": "4052", "listImage": "list_amaran200d_n", "name": "amaran 200d", "nomalImage": "alight_amaran_200d_n", "selectedImage": "alight_amaran_200d_p", "statisticsImage": "statistics_amaran200d_n", "type": 3, "combinationType": 14, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran 200 Series", "power": 250}, {"elementId": "4000", "listImage": "list_novap300c_n", "name": "Nova P300c", "nomalImage": "alight_novap300c_n", "selectedImage": "alight_novap300c_p", "statisticsImage": "statistics_novap300c_n", "type": 3, "combinationType": 1, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Nova", "power": 360}, {"elementId": "4001", "listImage": "list_ls1s_n", "name": "LS 1s", "nomalImage": "alight_ls1s_n", "selectedImage": "alight_ls1s_p", "statisticsImage": "statistics_ls1s_n", "type": 3, "combinationType": 4, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 120}, {"elementId": "4002", "listImage": "list_ls1c_n", "name": "LS 1c", "nomalImage": "alight_ls1c_n", "selectedImage": "alight_ls1c_p", "statisticsImage": "statistics_ls1c_n", "type": 3, "combinationType": 4, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 120}, {"elementId": "4003", "listImage": "list_ls12w_n", "name": "LS 1/2w", "nomalImage": "alight_ls12w_n", "selectedImage": "alight_ls12w_p", "statisticsImage": "statistics_ls12w_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 60}, {"elementId": "4004", "listImage": "list_lsc300d_n", "name": "LS C300d", "nomalImage": "alight_lsc300d_n", "selectedImage": "alight_lsc300d_p", "statisticsImage": "statistics_lsc300d", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 300}, {"elementId": "4005", "listImage": "list_lsc120d_n", "name": "LS C120d", "nomalImage": "alight_ls120d_n", "selectedImage": "alight_ls120d_p", "statisticsImage": "statistics_ls120d_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 130}, {"elementId": "4006", "listImage": "list_lsc120t_n", "name": "LS C120t", "nomalImage": "alight_ls120t_n", "selectedImage": "alight_ls120t_p", "statisticsImage": "statistics_ls120t_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 120}, {"elementId": "4007", "listImage": "list_lsc300d2_n", "name": "LS C300dII", "nomalImage": "alight_lsc300d2_n", "selectedImage": "alight_lsc300d2_p", "statisticsImage": "statistics_300d2_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 340}, {"elementId": "4008", "listImage": "list_hr672w_n", "name": "HR 672w", "nomalImage": "alight_hr672w_n", "selectedImage": "alight_hr672w_p", "statisticsImage": "statistics_hr672w_n", "type": 3, "combinationType": 4, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "Series HR 672", "power": 45}, {"elementId": "4009", "listImage": "list_hr672s_n", "name": "HR 672s", "nomalImage": "alight_hr672s_n", "selectedImage": "alight_hr672s_p", "statisticsImage": "statistics_hr672s_n", "type": 3, "combinationType": 4, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "Series HR 672", "power": 45}, {"elementId": "4010", "listImage": "list_hr672c_n", "name": "HR 672c", "nomalImage": "alight_hr672c_n", "selectedImage": "alight_hr672c_p", "statisticsImage": "statistics_hr672c_n", "type": 3, "combinationType": 4, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "Series HR 672", "power": 45}, {"elementId": "4011", "listImage": "list_tri8c_n", "name": "Tri 8c", "nomalImage": "alight_tri8c_n", "selectedImage": "alight_tri8c_p", "statisticsImage": "statistics_tri8c_n", "type": 3, "combinationType": 4, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "Series Tri", "power": 60}, {"elementId": "4012", "listImage": "list_tri8s_n", "name": "Tri 8s", "nomalImage": "alight_tri8s_n", "selectedImage": "alight_tri8s_p", "statisticsImage": "statistics_tri8s_n", "type": 3, "combinationType": 4, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "Series Tri", "power": 60}, {"elementId": "4013", "listImage": "list_accentb7c_n", "name": "B7c", "nomalImage": "alight_accentb7c_n", "selectedImage": "alight_accentb7c_p", "statisticsImage": "statistics_accentb7c_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "Aputure", "lightingSubClass": "Series Accent", "power": 7}, {"elementId": "4014", "listImage": "list_al198_n", "name": "AL-198", "nomalImage": "alight_al198c_n", "selectedImage": "alight_al198c_p", "statisticsImage": "statistics_al198c_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Mini", "power": 13}, {"elementId": "4015", "listImage": "list_alf7_n", "name": "AL-F7", "nomalImage": "alight_alf7_n", "selectedImage": "alight_alf7_p", "statisticsImage": "statistics_alf7_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Mini", "power": 13}, {"elementId": "4016", "listImage": "list_alm9_n", "name": "AL-M9", "nomalImage": "alight_alm9_n", "selectedImage": "alight_alm9_p", "statisticsImage": "statistics_alm9_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "Aputure", "lightingSubClass": "Series Mini", "power": 5}, {"elementId": "4017", "listImage": "list_almx_n", "name": "AL-MX", "nomalImage": "alight_almx_n", "selectedImage": "alight_almx_p", "statisticsImage": "statistics_almx_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "Aputure", "lightingSubClass": "Series Mini", "power": 9}, {"elementId": "4018", "listImage": "list_almw_n", "name": "AL-MW", "nomalImage": "alight_almw_n", "selectedImage": "alight_almw_p", "statisticsImage": "statistics_almw_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "Aputure", "lightingSubClass": "Series Mini", "power": 13}, {"elementId": "4019", "listImage": "list_lsmini20d_n", "name": "LS min120d", "nomalImage": "alight_lsmini20d_n", "selectedImage": "alight_lsmini20d_p", "statisticsImage": "statistics_lsmini20d_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Mini", "power": 30}, {"elementId": "4020", "listImage": "list_lsmini20c_n", "name": "LS min120c", "nomalImage": "alight_lsmini20c_n", "selectedImage": "alight_lsmini20c_p", "statisticsImage": "statistics_lsmini20c_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Mini", "power": 30}, {"elementId": "4021", "listImage": "list_almc_n", "name": "AL-MC", "nomalImage": "alight_almc_n", "selectedImage": "alight_almc_p", "statisticsImage": "statistics_almc_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "Aputure", "lightingSubClass": "Series Mini", "power": 5}, {"elementId": "4044", "listImage": "list_ls60x_n", "name": "LS 60x", "nomalImage": "alight_ls60x_n", "selectedImage": "alight_ls60x_p", "statisticsImage": "statistics_ls60x_n", "type": 3, "combinationType": 4, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 80}, {"elementId": "4045", "listImage": "list_ls60d_n", "name": "LS 60d", "nomalImage": "alight_ls60d_n", "selectedImage": "alight_ls60d_p", "statisticsImage": "statistics_ls60d_n", "type": 3, "combinationType": 4, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 80}, {"elementId": "4046", "listImage": "list_ls600dpro_n", "name": "LS 600d Pro", "nomalImage": "alight_ls600dpro_n", "selectedImage": "alight_ls600dpro_p", "statisticsImage": "statistics_ls600dpro_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 720}, {"elementId": "4047", "listImage": "list_ls300x_n", "name": "LS 300x", "nomalImage": "alight_ls300x_n", "selectedImage": "alight_ls300x_p", "statisticsImage": "statistics_300x_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 600}, {"elementId": "4048", "listImage": "list_lsc120dii_n", "name": "LS C120dII", "nomalImage": "scene_aputure_lsc120dii_n", "selectedImage": "scene_aputure_lsc120dii_p", "statisticsImage": "statistics_lsc120dii", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 180}, {"elementId": "4053", "listImage": "list_orbiter_n", "name": "Orbiter", "nomalImage": "scene_arri_orbiter_n", "selectedImage": "scene_arri_orbiter_p", "statisticsImage": "statistics_orbiter_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "Orbiter", "power": 500}, {"elementId": "4054", "listImage": "list_s120c_n", "name": "S120-C", "nomalImage": "scene_arri_s120c_n", "selectedImage": "scene_arri_s120c_p", "statisticsImage": "statistics_s120c_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "SkyPanel", "power": 430}, {"elementId": "4055", "listImage": "list_s360c_n", "name": "S360-C", "nomalImage": "scene_arri_s360c_n", "selectedImage": "scene_arri_s360c_p", "statisticsImage": "statistics_s360c_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "SkyPanel", "power": 1600}, {"elementId": "4056", "listImage": "list_s60c_n", "name": "S60-C", "nomalImage": "scene_arri_s60c_n", "selectedImage": "scene_arri_s60c_p", "statisticsImage": "statistics_s60c_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "SkyPanel", "power": 450}, {"elementId": "4057", "listImage": "list_s30c_n", "name": "S30-C", "nomalImage": "scene_arri_s30c_n", "selectedImage": "scene_arri_s30c_p", "statisticsImage": "statistics_s30c_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "SkyPanel", "power": 220}, {"elementId": "4058", "listImage": "list_s30rp_n", "name": "S30-RP", "nomalImage": "scene_arri_s30rp_n", "selectedImage": "scene_arri_s30rp_p", "statisticsImage": "statistics_s30rp_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "SkyPanel", "power": 200}, {"elementId": "4059", "listImage": "list_s60rp_n", "name": "S60-RP", "nomalImage": "scene_arri_s60rp_n", "selectedImage": "scene_arri_s60rp_p", "statisticsImage": "statistics_s60rp_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "SkyPanel", "power": 420}, {"elementId": "4060", "listImage": "list_l5c_n", "name": "L5-C", "nomalImage": "scene_arri_l5c_n", "selectedImage": "scene_arri_l5c_p", "statisticsImage": "statistics_l5c_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "L-Series C", "power": 115}, {"elementId": "4061", "listImage": "list_l7c_n", "name": "L7-C", "nomalImage": "scene_arri_l7c_n", "selectedImage": "scene_arri_l7c_p", "statisticsImage": "statistics_l7c_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "L-Series C", "power": 220}, {"elementId": "4062", "listImage": "list_l10c_n", "name": "L10-C", "nomalImage": "scene_arri_l10c_n", "selectedImage": "scene_arri_l10c_p", "statisticsImage": "statistics_l10c_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "L-Series C", "power": 220}, {"elementId": "4063", "listImage": "list_l5dt_n", "name": "L5-DT", "nomalImage": "scene_arri_l5dt_n", "selectedImage": "scene_arri_l5dt_p", "statisticsImage": "statistics_l5dt_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "L-Series DT&TT", "power": 115}, {"elementId": "4064", "listImage": "list_l5tt_n", "name": "L5-TT", "nomalImage": "scene_arri_l5tt_n", "selectedImage": "scene_arri_l5tt_p", "statisticsImage": "statistics_l5tt_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "L-Series DT&TT", "power": 115}, {"elementId": "4065", "listImage": "list_l7dt_n", "name": "L7-DT", "nomalImage": "scene_arri_l7dt_n", "selectedImage": "scene_arri_l7dt_p", "statisticsImage": "statistics_l7dt_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "L-Series DT&TT", "power": 220}, {"elementId": "4066", "listImage": "list_l7tt_n", "name": "L7-TT", "nomalImage": "scene_arri_l7tt_n", "selectedImage": "scene_arri_l7tt_p", "statisticsImage": "statistics_l7tt_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "L-Series DT&TT", "power": 220}, {"elementId": "4067", "listImage": "list_l10dt_n", "name": "L10-DT", "nomalImage": "scene_arri_l10dt_n", "selectedImage": "scene_arri_l10dt_p", "statisticsImage": "statistics_l10dt_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "L-Series DT&TT", "power": 510}, {"elementId": "4068", "listImage": "list_l10tt_n", "name": "L10-TT", "nomalImage": "scene_arri_l10tt_n", "selectedImage": "scene_arri_l10tt_p", "statisticsImage": "statistics_l10tt_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "L-Series DT&TT", "power": 510}, {"elementId": "4069", "listImage": "list_broadcaster2p_n", "name": "BroadCaster 2 Plus", "nomalImage": "scene_arri_broadcaster2p_n", "selectedImage": "scene_arri_broadcaster2p_p", "statisticsImage": "statistics_broadcaster2p_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "Caster-Series", "power": 35}, {"elementId": "4070", "listImage": "list_locaster2plus_n", "name": "LoCaster 2 Plus", "nomalImage": "scene_arri_locaster2p_n", "selectedImage": "scene_arri_locaster2p_p", "statisticsImage": "statistics_locaster2plus_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "Caster-Series", "power": 35}, {"elementId": "4071", "listImage": "list_m8_n", "name": "M8", "nomalImage": "scene_arri_m8_n", "selectedImage": "scene_arri_m8_p", "statisticsImage": "statistics_m8_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "M-Series", "power": 800}, {"elementId": "4072", "listImage": "list_m18_n", "name": "M18", "nomalImage": "scene_arri_m18_n", "selectedImage": "scene_arri_m18_p", "statisticsImage": "statistics_m18_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "M-Series", "power": 1800}, {"elementId": "4073", "listImage": "list_m40_n", "name": "M40", "nomalImage": "scene_arri_m40_n", "selectedImage": "scene_arri_m40_p", "statisticsImage": "statistics_m40_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "M-Series", "power": 4000}, {"elementId": "4074", "listImage": "list_m90_n", "name": "M90", "nomalImage": "scene_arri_m90_n", "selectedImage": "scene_arri_m90_p", "statisticsImage": "statistics_m90_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "M-Series", "power": 9000}, {"elementId": "4075", "listImage": "list_arrimax1812_n", "name": "ARRIMAX18/12", "nomalImage": "scene_arri_max1812_n", "selectedImage": "scene_arri_max1812_p", "statisticsImage": "statistics_arrimax1812_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "M-Series", "power": 18000}, {"elementId": "4076", "listImage": "list_arrisun5event_n", "name": "ARRISUN 5 Event", "nomalImage": "scene_arri_sun5event_n", "selectedImage": "scene_arri_sun5event_p", "statisticsImage": "statistics_arrisun5event_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRISUN Event", "power": 575}, {"elementId": "4077", "listImage": "list_arrisun18event_n", "name": "ARRISUN 18 Event", "nomalImage": "scene_arri_sun18event_n", "selectedImage": "scene_arri_sun18event_p", "statisticsImage": "statistics_arrisun18event_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRISUN Event", "power": 1800}, {"elementId": "4078", "listImage": "list_d5_n", "name": "D5", "nomalImage": "scene_arri_d5_n", "selectedImage": "scene_arri_d5_p", "statisticsImage": "statistics_d5_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue D", "power": 575}, {"elementId": "4079", "listImage": "list_d12_n", "name": "D12", "nomalImage": "scene_arri_d12_n", "selectedImage": "scene_arri_d12_p", "statisticsImage": "statistics_d12_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue D", "power": 1200}, {"elementId": "4080", "listImage": "list_d25_n", "name": "D25", "nomalImage": "scene_arri_d25_n", "selectedImage": "scene_arri_d25_p", "statisticsImage": "statistics_d25_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue D", "power": 2500}, {"elementId": "4081", "listImage": "list_d40_n", "name": "D40", "nomalImage": "scene_arri_d40_n", "selectedImage": "scene_arri_d40_p", "statisticsImage": "statistics_d40_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue D", "power": 4000}, {"elementId": "4082", "listImage": "list_daylight1812_n", "name": "ARRI Daylight 18/12", "nomalImage": "scene_arri_daylight1812_n", "selectedImage": "scene_arri_daylight1812_p", "statisticsImage": "statistics_daylight1812_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Daylight 18/12", "power": 18000}, {"elementId": "4083", "listImage": "list_2500theater_n", "name": "Compact 2500 Theater", "nomalImage": "scene_arri_2500theater_n", "selectedImage": "scene_arri_2500theater_p", "statisticsImage": "statistics_2500theater_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Compact Theater", "power": 2500}, {"elementId": "4084", "listImage": "list_4000theater_n", "name": "Compact 4000 Theater", "nomalImage": "scene_arri_4000theater_n", "selectedImage": "scene_arri_4000theater_p", "statisticsImage": "statistics_4000theater_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Compact Theater", "power": 4000}, {"elementId": "4085", "listImage": "list_arrisun2_n", "name": "ARRISUN 2", "nomalImage": "scene_arri_arrisun2_n", "selectedImage": "scene_arri_arrisun2_p", "statisticsImage": "statistics_arrisun2_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRISUN", "power": 200}, {"elementId": "4086", "listImage": "list_arrisun5_n", "name": "ARRISUN 5", "nomalImage": "scene_arri_arrisun5_n", "selectedImage": "scene_arri_arrisun5_p", "statisticsImage": "statistics_arrisun5_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRISUN", "power": 575}, {"elementId": "4087", "listImage": "list_as18_n", "name": "AS 18", "nomalImage": "scene_arri_as18_n", "selectedImage": "scene_arri_as18_p", "statisticsImage": "statistics_as18_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRISUN", "power": 1800}, {"elementId": "4088", "listImage": "list_as4025_n", "name": "AS 40/25", "nomalImage": "scene_arri_as4025_n", "selectedImage": "scene_arri_as4025_p", "statisticsImage": "statistics_as4025_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRISUN", "power": 4000}, {"elementId": "4089", "listImage": "list_arrisun60_n", "name": "ARRISUN 60", "nomalImage": "scene_arri_arrisun60_n", "selectedImage": "scene_arri_arrisun60_p", "statisticsImage": "statistics_arrisun60_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRISUN", "power": 6000}, {"elementId": "4090", "listImage": "list_arrisun120_n", "name": "ARRISUN 120", "nomalImage": "scene_arri_arrisun120_n", "selectedImage": "scene_arri_arrisun120_p", "statisticsImage": "statistics_arrisun120_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRISUN", "power": 12000}, {"elementId": "4091", "listImage": "list_compact200_n", "name": "ARRI Compact 200", "nomalImage": "scene_arri_compact200_n", "selectedImage": "scene_arri_compact200_p", "statisticsImage": "statistics_compact200_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Compact", "power": 200}, {"elementId": "4092", "listImage": "list_compact6000p_n", "name": "ARRI Compact 6000 Plus", "nomalImage": "scene_arri_compact6kplus_n", "selectedImage": "scene_arri_compact6kplus_p", "statisticsImage": "statistics_compact6000p_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Compact", "power": 6000}, {"elementId": "4093", "listImage": "list_compact12000baby_n", "name": "ARRI Compact 12000 Plus", "nomalImage": "scene_arri_compact12kbaby_n", "selectedImage": "scene_arri_compact12kbaby_p", "statisticsImage": "statistics_compact12000baby_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Compact", "power": 12000}, {"elementId": "4094", "listImage": "list_pocketpar125_n", "name": "ARRILUX POCKETPAR 125", "nomalImage": "scene_arri_pocketpar125_n", "selectedImage": "scene_arri_pocketpar125_p", "statisticsImage": "statistics_pocketpar125_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRILUX", "power": 125}, {"elementId": "4095", "listImage": "list_pocketpar200_n", "name": "ARRILUX POCKETPAR 200", "nomalImage": "scene_arri_pocketpar200_n", "selectedImage": "scene_arri_pocketpar200_p", "statisticsImage": "statistics_pocketpar200_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRILUX", "power": 200}, {"elementId": "4096", "listImage": "list_pocketpar400_n", "name": "ARRILUX POCKETPAR 400", "nomalImage": "scene_arri_pocketpar400_n", "selectedImage": "scene_arri_pocketpar400_p", "statisticsImage": "statistics_pocketpar400_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRILUX", "power": 400}, {"elementId": "4097", "listImage": "list_arrix5_n", "name": "ARRI X 5", "nomalImage": "scene_arri_arrix5_n", "selectedImage": "scene_arri_arrix5_p", "statisticsImage": "statistics_arrix5_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI X/X Theater", "power": 575}, {"elementId": "4098", "listImage": "list_arrix12_n", "name": "ARRI X 12", "nomalImage": "scene_arri_arrix12_n", "selectedImage": "scene_arri_arrix12_p", "statisticsImage": "statistics_arrix12_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI X/X Theater", "power": 1200}, {"elementId": "4099", "listImage": "list_arrix4025_n", "name": "ARRI X 40/25", "nomalImage": "scene_arri_arrix4025_n", "selectedImage": "scene_arri_arrix4025_p", "statisticsImage": "statistics_arrix4025_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI X/X Theater", "power": 4000}, {"elementId": "4100", "listImage": "list_arrix60_n", "name": "ARRI X 60", "nomalImage": "scene_arri_arrix60_n", "selectedImage": "scene_arri_arrix60_p", "statisticsImage": "statistics_arrix60_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI X/X Theater", "power": 6000}, {"elementId": "4101", "listImage": "list_arrix4025theater_n", "name": "ARRI X 40/25 Theater", "nomalImage": "scene_arri_x4025theater_n", "selectedImage": "scene_arri_x4025theater_p", "statisticsImage": "statistics_arrix4025theater_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI X/X Theater", "power": 4000}, {"elementId": "4102", "listImage": "list_arrilite750p_n", "name": "ARRILITE 750 Plus", "nomalImage": "scene_arri_lite750plus_n", "selectedImage": "scene_arri_lite750plus_p", "statisticsImage": "statistics_arrilite750p_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRILITE Plus", "power": 800}, {"elementId": "4103", "listImage": "list_arrilite2000p_n", "name": "ARRILITE 2000 Plus", "nomalImage": "scene_arri_lite2000plus_n", "selectedImage": "scene_arri_lite2000plus_p", "statisticsImage": "statistics_arrilite2000p_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRILITE Plus", "power": 2000}, {"elementId": "4104", "listImage": "list_arri150_n", "name": "ARRI 150", "nomalImage": "scene_arri_arri150_n", "selectedImage": "scene_arri_arri150_p", "statisticsImage": "statistics_arri150_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Junior", "power": 150}, {"elementId": "4105", "listImage": "list_arri300p_n", "name": "ARRI 300 Plus", "nomalImage": "scene_arri_arri300plus_n", "selectedImage": "scene_arri_arri300plus_p", "statisticsImage": "statistics_arri300p_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Junior", "power": 300}, {"elementId": "4106", "listImage": "list_arri650p_n", "name": "ARRI 650 Plus", "nomalImage": "scene_arri_arri650plus_n", "selectedImage": "scene_arri_arri650plus_p", "statisticsImage": "statistics_arri650p_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Junior", "power": 650}, {"elementId": "4107", "listImage": "list_truebluet1_n", "name": "True Blue T1", "nomalImage": "scene_arri_truebluet1_n", "selectedImage": "scene_arri_truebluet1_p", "statisticsImage": "statistics_truebluet1_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue T", "power": 1000}, {"elementId": "4108", "listImage": "list_truebluet2_n", "name": "True Blue T2", "nomalImage": "scene_arri_truebluet2_n", "selectedImage": "scene_arri_truebluet2_p", "statisticsImage": "statistics_truebluet2_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue T", "power": 2000}, {"elementId": "4109", "listImage": "list_truebluet5_n", "name": "True Blue T5", "nomalImage": "scene_arri_truebluet5_n", "selectedImage": "scene_arri_truebluet5_p", "statisticsImage": "statistics_truebluet5_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue T", "power": 3000}, {"elementId": "4110", "listImage": "list_truebluest1_n", "name": "True Blue ST1", "nomalImage": "scene_arri_truebluest1_n", "selectedImage": "scene_arri_truebluest1_p", "statisticsImage": "statistics_truebluest1_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue ST", "power": 1000}, {"elementId": "4111", "listImage": "list_truebluest23_n", "name": "True Blue ST2/3", "nomalImage": "scene_arri_truebluest23_n", "selectedImage": "scene_arri_truebluest23_p", "statisticsImage": "statistics_truebluest23_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue ST", "power": 3000}, {"elementId": "4112", "listImage": "list_truebluetst5_n", "name": "True Blue ST5", "nomalImage": "scene_arri_truebluest5_n", "selectedImage": "scene_arri_truebluest5_p", "statisticsImage": "statistics_truebluetst5_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue ST", "power": 5000}, {"elementId": "4113", "listImage": "list_st12theater_n", "name": "True Blue ST1/2 Theater", "nomalImage": "scene_arri_st12theater_n", "selectedImage": "scene_arri_st12theater_p", "statisticsImage": "statistics_st12theater_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue ST Theater", "power": 2000}, {"elementId": "4114", "listImage": "list_st5theater_n", "name": "True Blue ST5 Theater", "nomalImage": "scene_arri_st5theater_n", "selectedImage": "scene_arri_st5theater_p", "statisticsImage": "statistics_st5theater_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "True Blue ST Theater", "power": 5000}, {"elementId": "4115", "listImage": "list_studiot12_n", "name": "Studio T12", "nomalImage": "scene_arri_studiot12_n", "selectedImage": "scene_arri_studiot12_p", "statisticsImage": "statistics_studiot12_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "Studio T", "power": 12000}, {"elementId": "4116", "listImage": "list_studiot24_n", "name": "Studio T24", "nomalImage": "scene_arri_studiot24_n", "selectedImage": "scene_arri_studiot24_p", "statisticsImage": "statistics_studiot24_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "Studio T", "power": 24000}, {"elementId": "4117", "listImage": "list_cyc1250_n", "name": "ARRI Cyc 1250", "nomalImage": "scene_arri_cyc1250_n", "selectedImage": "scene_arri_cyc1250_p", "statisticsImage": "statistics_cyc1250_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Cyc / Flood Light", "power": 1250}, {"elementId": "4118", "listImage": "list_flood1250_n", "name": "ARRI Flood 1250", "nomalImage": "scene_arri_flood1250_n", "selectedImage": "scene_arri_flood1250_p", "statisticsImage": "statistics_flood1250_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ARRI", "lightingSubClass": "ARRI Cyc / Flood Light", "power": 1250}, {"elementId": "4119", "listImage": "list_nyxbulb_n", "name": "NYX Bulb", "nomalImage": "scene_astera_nyxbulb_n", "selectedImage": "scene_astera_nyxbulb_p", "statisticsImage": "statistics_nyxbulb_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "ASTERA", "lightingSubClass": "Bulb", "power": 10}, {"elementId": "4120", "listImage": "list_pixeltube_n", "name": "AX1 Pixeltube", "nomalImage": "scene_astera_ax1pixeltube_n", "selectedImage": "scene_astera_ax1pixeltube_p", "statisticsImage": "statistics_pixeltube_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "ASTERA", "lightingSubClass": "<PERSON><PERSON>", "power": 28}, {"elementId": "4121", "listImage": "list_titantube_n", "name": "Titan Tube", "nomalImage": "scene_astera_titantube_n", "selectedImage": "scene_astera_titantube_p", "statisticsImage": "statistics_titantube_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "ASTERA", "lightingSubClass": "<PERSON><PERSON>", "power": 48}, {"elementId": "4122", "listImage": "list_heliostube_n", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "scene_astera_heliostube_n", "selectedImage": "scene_astera_heliostube_p", "statisticsImage": "statistics_heliostube_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "ASTERA", "lightingSubClass": "<PERSON><PERSON>", "power": 24}, {"elementId": "4123", "listImage": "list_hyperiontube_n", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "scene_astera_hyperiontube_n", "selectedImage": "scene_astera_hyperiontube_p", "statisticsImage": "statistics_hyperiontube_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "ASTERA", "lightingSubClass": "<PERSON><PERSON>", "power": 92}, {"elementId": "4124", "listImage": "list_ax3lightdrop_n", "name": "AX3 Lightdrop", "nomalImage": "scene_astera_ax3lightdrop_n", "selectedImage": "scene_astera_ax3lightdrop_p", "statisticsImage": "statistics_ax3lightdrop_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "ASTERA", "lightingSubClass": "Par", "power": 15}, {"elementId": "4125", "listImage": "list_ax5riplepar_n", "name": "AX5 Triplepar", "nomalImage": "scene_astera_ax5triplepar_n", "selectedImage": "scene_astera_ax5triplepar_p", "statisticsImage": "statistics_ax5riplepar_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "ASTERA", "lightingSubClass": "Par", "power": 45}, {"elementId": "4126", "listImage": "list_ax7spotlite_n", "name": "AX7 Spotlite", "nomalImage": "scene_astera_ax7spotlite_n", "selectedImage": "scene_astera_ax7spotlite_p", "statisticsImage": "statistics_ax7spotlite_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "ASTERA", "lightingSubClass": "Par", "power": 60}, {"elementId": "4127", "listImage": "list_ax10spotmaxe_n", "name": "AX10 Spotmax", "nomalImage": "scene_astera_ax10spotmax_n", "selectedImage": "scene_astera_ax10spotmax_p", "statisticsImage": "statistics_ax10spotmaxe_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "ASTERA", "lightingSubClass": "Par", "power": 135}, {"elementId": "4128", "listImage": "list_ax2pixelbar20_n", "name": "AX2 Pixelbar(20”)", "nomalImage": "scene_astera_ax2pixelbar20_n", "selectedImage": "scene_astera_ax2pixelbar20_p", "statisticsImage": "statistics_ax2pixelbar20_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "ASTERA", "lightingSubClass": "Bar", "power": 40}, {"elementId": "4129", "listImage": "list_ax2pixelbar40_n", "name": "AX2 Pixelbar(40”)", "nomalImage": "scene_astera_ax2pixelbar40_n", "selectedImage": "scene_astera_ax2pixelbar40_p", "statisticsImage": "statistics_ax2pixelbar40_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "ASTERA", "lightingSubClass": "Bar", "power": 80}, {"elementId": "4130", "listImage": "list_t44t24leddmx_n", "name": "FreeStyle T44/T24 LED DMX", "nomalImage": "scene_kino_freestylet44t24_n", "selectedImage": "scene_kino_freestylet44t24_p", "statisticsImage": "statistics_t44t24leddmx_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Remote Controller", "power": 150}, {"elementId": "4131", "listImage": "list_t42t22leddmx_n", "name": "FreeStyle T42/T22 LED DMX", "nomalImage": "scene_kino_freestylet42t22_n", "selectedImage": "scene_kino_freestylet42t22_p", "statisticsImage": "statistics_t42t22leddmx_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Remote Controller", "power": 150}, {"elementId": "4132", "listImage": "list_t41t21leddmx_n", "name": "FreeStyle T41/T21 LED DMX", "nomalImage": "scene_kino_freestylet41t21_n", "selectedImage": "scene_kino_freestylet41t21_p", "statisticsImage": "statistics_t41t21leddmx_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Remote Controller", "power": 100}, {"elementId": "4133", "listImage": "list_minileddmx_n", "name": "FreeStyle Mini LED DMX", "nomalImage": "scene_kino_freestylemini_n", "selectedImage": "scene_kino_freestylemini_p", "statisticsImage": "statistics_minileddmx_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Remote Controller", "power": 100}, {"elementId": "4134", "listImage": "list_freestyle41led_n", "name": "FreeStyle 41 LED", "nomalImage": "scene_kino_freestyle41_n", "selectedImage": "scene_kino_freestyle41_p", "statisticsImage": "statistics_freestyle41led_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Remote Controller", "power": 150}, {"elementId": "4135", "listImage": "list_freestyle31led_n", "name": "FreeStyle 31 LED", "nomalImage": "scene_kino_freestyle31_n", "selectedImage": "scene_kino_freestyle31_p", "statisticsImage": "statistics_freestyle31led_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Remote Controller", "power": 150}, {"elementId": "4136", "listImage": "list_freestyle21led_n", "name": "FreeStyle 21 LED", "nomalImage": "scene_kino_freestyle21_n", "selectedImage": "scene_kino_freestyle21_p", "statisticsImage": "statistics_freestyle21led_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Remote Controller", "power": 150}, {"elementId": "4137", "listImage": "list_celeb850_n", "name": "Celeb 850 LED DMX", "nomalImage": "scene_kino_celeb850_n", "selectedImage": "scene_kino_celeb850_p", "statisticsImage": "statistics_celeb850_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 575}, {"elementId": "4138", "listImage": "list_celeb450_n", "name": "Celeb 450 LED DMX", "nomalImage": "scene_kino_celeb450_n", "selectedImage": "scene_kino_celeb450_p", "statisticsImage": "statistics_celeb450_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 255}, {"elementId": "4139", "listImage": "list_celeb450q_n", "name": "Celeb 450Q LED DMX", "nomalImage": "scene_kino_celeb450q_n", "selectedImage": "scene_kino_celeb450q_p", "statisticsImage": "statistics_celeb450q_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 270}, {"elementId": "4140", "listImage": "list_celeb250_n", "name": "Celeb 250 LED DMX", "nomalImage": "scene_kino_celeb250_n", "selectedImage": "scene_kino_celeb250_p", "statisticsImage": "statistics_celeb250_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 145}, {"elementId": "4141", "listImage": "list_divalite41_n", "name": "Diva-Lite 41 LED DMX", "nomalImage": "scene_kino_divalite41_n", "selectedImage": "scene_kino_divalite41_p", "statisticsImage": "statistics_divalite41_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 150}, {"elementId": "4142", "listImage": "list_divalite31_n", "name": "Diva-Lite 31 LED DMX", "nomalImage": "scene_kino_divalite31_n", "selectedImage": "scene_kino_divalite31_p", "statisticsImage": "statistics_divalite31_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 150}, {"elementId": "4143", "listImage": "list_divalite21_n", "name": "Diva-Lite 21 LED DMX", "nomalImage": "scene_kino_divalite21_n", "selectedImage": "scene_kino_divalite21_p", "statisticsImage": "statistics_divalite21_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 150}, {"elementId": "4144", "listImage": "list_divalite30_n", "name": "Diva-Lite 30 LED DMX", "nomalImage": "scene_kino_divalite30_n", "selectedImage": "scene_kino_divalite30_p", "statisticsImage": "statistics_divalite30_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 150}, {"elementId": "4145", "listImage": "list_divalite20_n", "name": "Diva-Lite 20 LED DMX", "nomalImage": "scene_kino_divalite20_n", "selectedImage": "scene_kino_divalite20_p", "statisticsImage": "statistics_divalite20_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 150}, {"elementId": "4146", "listImage": "list_imagel80_n", "name": "Image L80 LED DMX", "nomalImage": "scene_kino_miagel80_n", "selectedImage": "scene_kino_miagel80_p", "statisticsImage": "statistics_imagel80_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 400}, {"elementId": "4147", "listImage": "list_gemini1x1_n", "name": "Gemini 1x1", "nomalImage": "scene_litepanels_gemini1x1_n", "selectedImage": "scene_litepanels_gemini1x1_p", "statisticsImage": "statistics_gemini1x1_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Gemini", "power": 200}, {"elementId": "4148", "listImage": "list_gemini2x1_n", "name": "Gemini 2x1", "nomalImage": "scene_litepanels_gemini2x1_n", "selectedImage": "scene_litepanels_gemini2x1_p", "statisticsImage": "statistics_gemini2x1_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Gemini", "power": 325}, {"elementId": "4149", "listImage": "list_astra6xdaylight_n", "name": "Astra 6X Daylight", "nomalImage": "scene_litepanels_astra6xdaylight_n", "selectedImage": "scene_litepanels_astra6xdaylight_p", "statisticsImage": "statistics_astra6xdaylight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Astra", "power": 105}, {"elementId": "4150", "listImage": "list_astra6xbicolor_n", "name": "Astra 6X Bi-Color", "nomalImage": "scene_litepanels_astra6xbicolor_n", "selectedImage": "scene_litepanels_astra6xbicolor_p", "statisticsImage": "statistics_astra6xbicolor_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Astra", "power": 105}, {"elementId": "4151", "listImage": "list_astra3xdaylight_n", "name": "Astra 3X Daylight", "nomalImage": "scene_litepanels_astra3xdaylight_n", "selectedImage": "scene_litepanels_astra3xdaylight_p", "statisticsImage": "statistics_astra3xdaylight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Astra", "power": 55}, {"elementId": "4152", "listImage": "list_astra3xbicolor_n", "name": "Astra 3X Bi-Color", "nomalImage": "scene_litepanels_astra3xbicolor_n", "selectedImage": "scene_litepanels_astra3xbicolor_p", "statisticsImage": "statistics_astra3xbicolor_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Astra", "power": 55}, {"elementId": "4153", "listImage": "list_astrabifocusdaylight_n", "name": "Astra Bi-Focus Daylight", "nomalImage": "scene_litepanels_bifocusdaylight_n", "selectedImage": "scene_litepanels_bifocusdaylight_p", "statisticsImage": "statistics_astrabifocusdaylight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Astra", "power": 105}, {"elementId": "4154", "listImage": "list_astrasoftbicolor_n", "name": "Astra Soft Bi-Color", "nomalImage": "scene_litepanels_softbicolor_n", "selectedImage": "scene_litepanels_softbicolor_p", "statisticsImage": "statistics_astrasoftbicolor_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Astra", "power": 105}, {"elementId": "4155", "listImage": "list_sola6_n", "name": "Sola 6+", "nomalImage": "scene_litepanels_sola6_n", "selectedImage": "scene_litepanels_sola6_p", "statisticsImage": "statistics_sola6_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Sola", "power": 104}, {"elementId": "4156", "listImage": "list_sola4_n", "name": "Sola 4+", "nomalImage": "scene_litepanels_sola4_n", "selectedImage": "scene_litepanels_sola4_p", "statisticsImage": "statistics_sola4_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Sola", "power": 53}, {"elementId": "4157", "listImage": "list_solaeng_n", "name": "Sola ENG", "nomalImage": "scene_litepanels_solaeng_n", "selectedImage": "scene_litepanels_solaeng_p", "statisticsImage": "statistics_solaeng_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Sola", "power": 30}, {"elementId": "4158", "listImage": "list_inca4_n", "name": "Inca 4", "nomalImage": "scene_litepanels_inca4_n", "selectedImage": "scene_litepanels_inca4_p", "statisticsImage": "statistics_inca4_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Inca", "power": 30}, {"elementId": "4159", "listImage": "list_inca6_n", "name": "Inca 6", "nomalImage": "scene_litepanels_inca6_n", "selectedImage": "scene_litepanels_inca6_p", "statisticsImage": "statistics_inca6_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "Litepanels", "lightingSubClass": "Inca", "power": 104}, {"elementId": "4160", "listImage": "list_6varibaby_n", "name": "6\" Vari-Baby LED", "nomalImage": "scene_mole_6varibaby_n", "selectedImage": "scene_mole_6<PERSON><PERSON><PERSON>_p", "statisticsImage": "statistics_6varibaby_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARIABLE COLOR FRESNELS", "power": 140}, {"elementId": "4161", "listImage": "list_8varijunior_n", "name": "8\" Vari-Junior LED", "nomalImage": "scene_mole_8varibaby_n", "selectedImage": "scene_mole_8<PERSON><PERSON><PERSON>_p", "statisticsImage": "statistics_8varijunior_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARIABLE COLOR FRESNELS", "power": 180}, {"elementId": "4162", "listImage": "list_10varistudiojunior_n", "name": "10\" Vari-Studio Junior LED", "nomalImage": "variable_10varistudiojunior_n", "selectedImage": "variable_10varistudiojunior_p", "statisticsImage": "statistics_10varistudiojunior_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARIABLE COLOR FRESNELS", "power": 250}, {"elementId": "4163", "listImage": "list_10varisenior_n", "name": "10\" Vari-Senior LED", "nomalImage": "scene_mole_10varisenior_n", "selectedImage": "scene_mole_10varisenior_p", "statisticsImage": "statistics_10varisenior_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARIABLE COLOR FRESNELS", "power": 800}, {"elementId": "4164", "listImage": "list_14varitener_n", "name": "14\" Vari-Tener LED", "nomalImage": "scene_mole_14var<PERSON><PERSON>_n", "selectedImage": "scene_mole_14<PERSON><PERSON><PERSON>_p", "statisticsImage": "statistics_14varitener_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARIABLE COLOR FRESNELS", "power": 1500}, {"elementId": "4165", "listImage": "list_400varispacce_n", "name": "400W Vari-Space LED", "nomalImage": "variable_400varispace_n", "selectedImage": "variable_400varispace_p", "statisticsImage": "statistics_400varispacce_n", "type": 3, "combinationType": 2, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARIABLE SPACELITES", "power": 400}, {"elementId": "4166", "listImage": "list_9000varispace_n", "name": "900W Vari-Space LED", "nomalImage": "variable_900varispace_n", "selectedImage": "variable_900varispace_p", "statisticsImage": "statistics_9000varispace_n", "type": 3, "combinationType": 2, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARIABLE SPACELITES", "power": 900}, {"elementId": "4167", "listImage": "list_100varimole_n", "name": "100W Vari Mole LED", "nomalImage": "scene_mole_100varimole_n", "selectedImage": "scene_mole_100varimole_p", "statisticsImage": "statistics_100varimole_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARI-MOLES", "power": 100}, {"elementId": "4168", "listImage": "list_200varimole_n", "name": "200W Vari Mole LED", "nomalImage": "scene_mole_200varimole_n", "selectedImage": "scene_mole_200varimole_p", "statisticsImage": "statistics_200varimole_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARI-MOLES", "power": 200}, {"elementId": "4169", "listImage": "list_400varisoftpanel_n", "name": "400W Vari-Soft Panel LED", "nomalImage": "scene_mole_400varisoftpanel_n", "selectedImage": "scene_mole_400varisoftpanel_p", "statisticsImage": "statistics_400varisoftpanel_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARI-MOLES", "power": 400}, {"elementId": "4170", "listImage": "list_400varisoft_n", "name": "400W Vari-Soft LED", "nomalImage": "scene_mole_400varisoft_n", "selectedImage": "scene_mole_400varisoft_p", "statisticsImage": "statistics_400varisoft_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARI-MOLES", "power": 400}, {"elementId": "4171", "listImage": "list_200varisoft_n", "name": "200W Vari-Soft LED", "nomalImage": "scene_mole_200varisoft_n", "selectedImage": "scene_mole_200varisoft_p", "statisticsImage": "statistics_200varisoft_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "VARI-MOLES", "power": 200}, {"elementId": "4172", "listImage": "list_100tweenie_n", "name": "100W Tweenie LED", "nomalImage": "scene_mole_100tweenie_n", "selectedImage": "scene_mole_100tweenie_p", "statisticsImage": "statistics_100tweenie_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SINGLE COLOR FRESNELS", "power": 100}, {"elementId": "4173", "listImage": "list_150baby_n", "name": "150W Baby LED", "nomalImage": "scene_mole_150baby_n", "selectedImage": "scene_mole_150baby_p", "statisticsImage": "statistics_150baby_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SINGLE COLOR FRESNELS", "power": 150}, {"elementId": "4174", "listImage": "list_200junior_n", "name": "200W Junior LED", "nomalImage": "scene_mole_200junior_n", "selectedImage": "scene_mole_200junior_p", "statisticsImage": "statistics_200junior_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SINGLE COLOR FRESNELS", "power": 200}, {"elementId": "4175", "listImage": "list_400studiojunior_n", "name": "400W Studio Junior LED", "nomalImage": "scene_mole_400studiojunior_n", "selectedImage": "scene_mole_400studiojunior_p", "statisticsImage": "statistics_400studiojunior_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SINGLE COLOR FRESNELS", "power": 400}, {"elementId": "4176", "listImage": "list_900senior_n", "name": "900W Senior LED", "nomalImage": "scene_mole_900senior_n", "selectedImage": "scene_mole_900senior_p", "statisticsImage": "statistics_900senior_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SINGLE COLOR FRESNELS", "power": 900}, {"elementId": "4177", "listImage": "list_1600tener_n", "name": "1600W Tener LED", "nomalImage": "scene_mole_1600tener_n", "selectedImage": "scene_mole_<PERSON><PERSON>er_p", "statisticsImage": "statistics_1600tener_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SINGLE COLOR FRESNELS", "power": 1600}, {"elementId": "4177", "listImage": "list_20kled_n", "name": "20K LED", "nomalImage": "scene_mole_20kled_n", "selectedImage": "scene_mole_20kled_p", "statisticsImage": "statistics_20kled_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SINGLE COLOR FRESNELS", "power": 3000}, {"elementId": "4178", "listImage": "list_100moleprofresnel_n", "name": "100W Mole-Pro Fresnel", "nomalImage": "scene_mole_100molepro_n", "selectedImage": "scene_mole_100molepro_p", "statisticsImage": "statistics_100moleprofresnel_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE PRO", "power": 100}, {"elementId": "4179", "listImage": "list_50moleprofresnel_n", "name": "50W Mole-Pro Panel", "nomalImage": "scene_mole_50molepro_n", "selectedImage": "scene_mole_50molepro_p", "statisticsImage": "statistics_50moleprofresnel_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE PRO", "power": 50}, {"elementId": "4180", "listImage": "list_1218kwattfresnel_n", "name": "12000/18000 Watt Daylite Fresnel", "nomalImage": "scene_mole_1218kwattfresnel_n", "selectedImage": "scene_mole_1218kwattfresnel_p", "statisticsImage": "statistics_1218kwattfresnel_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE FRESNELS", "power": 18000}, {"elementId": "4181", "listImage": "list_24kwattfresnel_n", "name": "24000 Watt Daylite Fresnel", "nomalImage": "scene_mole_24kwattdaylite_n", "selectedImage": "scene_mole_24kwattdaylite_p", "statisticsImage": "statistics_24kwattfresnel_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE FRESNELS", "power": 2400}, {"elementId": "4182", "listImage": "list_100200inbet_n", "name": "100/200 Watt InBetweenie Solarspot®", "nomalImage": "scene_mole_100200inbet_n", "selectedImage": "scene_mole_100200inbet_p", "statisticsImage": "statistics_100200inbet_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "LITEWATE FRESNELS", "power": 250}, {"elementId": "4183", "listImage": "list_300kwattfresnel_n", "name": "300 Watt Betweenie Solarspot®", "nomalImage": "scene_mole_300betweennie_n", "selectedImage": "scene_mole_300betweennie_p", "statisticsImage": "statistics_300kwattfresnel_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "LITEWATE FRESNELS", "power": 300}, {"elementId": "4184", "listImage": "list_650tweenieiisolar_n", "name": "650 Watt Tweenie II Solarspot®", "nomalImage": "scene_mole_650tweenieii_n", "selectedImage": "scene_mole_650tweenieii_p", "statisticsImage": "statistics_650tweenieiisolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "LITEWATE FRESNELS", "power": 650}, {"elementId": "4185", "listImage": "list_1kt6babysolar_n", "name": "1000 Watt 6\" Baby Solarspot®", "nomalImage": "scene_mole_1000twatt6baby_n", "selectedImage": "scene_mole_1000watt6baby_p", "statisticsImage": "statistics_1kt6babysolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "LITEWATE FRESNELS", "power": 1000}, {"elementId": "4186", "listImage": "list_2kt8juniorsolar_n", "name": "2000 Watt 8\" Junior Solarspot®", "nomalImage": "scene_mole_2000watt8junior_n", "selectedImage": "scene_mole_2000watt8junior_p", "statisticsImage": "statistics_2kt8juniorsolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "LITEWATE FRESNELS", "power": 2000}, {"elementId": "4187", "listImage": "list_10juniorsolar_n", "name": "2000 Watt 10\" Junior Solarspot®", "nomalImage": "scene_mole_2000watt10junior_n", "selectedImage": "scene_mole_2000watt10junior_p", "statisticsImage": "statistics_10juniorsolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "LITEWATE FRESNELS", "power": 2000}, {"elementId": "4188", "listImage": "list_5k14seniorsolar_n", "name": "5000 Watt 14\" SeniorSolarspot®", "nomalImage": "scene_mole_5000watt14senior_n", "selectedImage": "scene_mole_5000watt14senior_p", "statisticsImage": "statistics_5k14seniorsolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "LITEWATE FRESNELS", "power": 5000}, {"elementId": "4189", "listImage": "list_1w12ktenersolar_n", "name": "10000/12000 Watt 20\" Tener  Solarspot®", "nomalImage": "scene_mole_1w12ktenersolar_n", "selectedImage": "scene_mole_1w12ktenersolar_p", "statisticsImage": "statistics_1w12ktenersolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "LITEWATE FRESNELS", "power": 12000}, {"elementId": "4190", "listImage": "list_10k12kbigeye_n", "name": "10000/12000 Watt \"BIG EYE\" Tener Solarspot®", "nomalImage": "scene_mole_10k12kbigeye_n", "selectedImage": "scene_mole_10k12kbigeye_p", "statisticsImage": "statistics_10k12kbigeye_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "LITEWATE FRESNELS", "power": 12000}, {"elementId": "4191", "listImage": "list_2kmolequartzsolar_n", "name": "20000 Watt Molequartz® Solarspot®", "nomalImage": "scene_mole_2000molequartz_n", "selectedImage": "scene_mole_2000molequartz_p", "statisticsImage": "statistics_2kmolequartzsolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "LITEWATE FRESNELS", "power": 2000}, {"elementId": "4192", "listImage": "list_1kbabybabysolar_n", "name": "1000 Watt Molequartz® Baby-Baby Solarspot®", "nomalImage": "scene_mole_1000wattbabybaby_n", "selectedImage": "scene_mole_1000wattbabybaby_p", "statisticsImage": "statistics_1kbabybabysolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "BABY FRESNELS", "power": 1000}, {"elementId": "4193", "listImage": "list_2kbabyjuniorsolar_n", "name": "2000 Watt Molequartz® Baby Junior Solarspot®", "nomalImage": "scene_mole_2000babyjunior_n", "selectedImage": "scene_mole_2000babyjunior_p", "statisticsImage": "statistics_2kbabyjuniorsolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "BABY FRESNELS", "power": 2000}, {"elementId": "4194", "listImage": "list_5kbabysneiorsolar_n", "name": "5000 Watt Molequartz® Baby Senior Solarspot®", "nomalImage": "scene_mole_5000babysenior_n", "selectedImage": "scene_mole_5000babysenior_p", "statisticsImage": "statistics_5kbabysneiorsolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "BABY FRESNELS", "power": 5000}, {"elementId": "4195", "listImage": "list_10kbabytenersolar_n", "name": "10000 Watt Molequartz® Baby Tener Solarspot®", "nomalImage": "scene_mole_1000babytener_n", "selectedImage": "scene_mole_1000babytener_p", "statisticsImage": "statistics_10kbabytenersolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "BABY FRESNELS", "power": 10000}, {"elementId": "4196", "listImage": "list_12kbabytewlversolar_n", "name": "12000 Watt Molequartz® Baby Twelver Solarspot®", "nomalImage": "scene_mole_12kbabytwelver_n", "selectedImage": "scene_mole_12kbabytwelver_p", "statisticsImage": "statistics_12kbabytewlversolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "BABY FRESNELS", "power": 12000}, {"elementId": "4197", "listImage": "list_200tinymolesolar_n", "name": "200 Watt Tiny-Mole Solarspot®", "nomalImage": "scene_mole_200tinymole_n", "selectedImage": "scene_mole_200tinymole_p", "statisticsImage": "statistics_200tinymolesolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "CLASSIC FRESNELS", "power": 200}, {"elementId": "4198", "listImage": "list_200minimolesolar_n", "name": "200 Watt Mini-Mole Solarspot®", "nomalImage": "scene_mole_200minimole_n", "selectedImage": "scene_mole_200minimole_p", "statisticsImage": "statistics_200minimolesolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "CLASSIC FRESNELS", "power": 200}, {"elementId": "4199", "listImage": "list_200midgetsolar_n", "name": "200 Watt Midget Solarspot®", "nomalImage": "scene_mole_200midget_n", "selectedImage": "scene_mole_200midget_p", "statisticsImage": "statistics_200midgetsolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "CLASSIC FRESNELS", "power": 200}, {"elementId": "4200", "listImage": "list_1k6babysolar_n", "name": "1000 Watt 6\" Baby Solarspot®", "nomalImage": "scene_mole_1000watt6baby_n", "selectedImage": "scene_mole_1000watt6baby_p", "statisticsImage": "statistics_1k6babysolar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "CLASSIC FRESNELS", "power": 1000}, {"elementId": "4201", "listImage": "list_575daylitepar_n", "name": "575 Watt Daylite Par®", "nomalImage": "scene_mole_575daylitepar_n", "selectedImage": "scene_mole_575daylitepar_p", "statisticsImage": "statistics_575daylitepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE PARS", "power": 575}, {"elementId": "4202", "listImage": "list_800daylitepar_n", "name": "800 Watt Daylite Par®", "nomalImage": "scene_mole_800daylitepar_n", "selectedImage": "scene_mole_800daylitepar_p", "statisticsImage": "statistics_800daylitepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE PARS", "power": 800}, {"elementId": "4203", "listImage": "list_1200daylitepar_n", "name": "1200 Watt Daylite Par®", "nomalImage": "scene_mole_1200daylitepar_n", "selectedImage": "scene_mole_1200daylitepar_p", "statisticsImage": "statistics_1200daylitepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE PARS", "power": 1200}, {"elementId": "4204", "listImage": "list_1218wattdaylitepar_n", "name": "1200/1800 Watt Daylite Par®", "nomalImage": "scene_mole_1218wattdaylitepar_n", "selectedImage": "scene_mole_1218wattdaylitepar_p", "statisticsImage": "statistics_1218wattdaylitepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE PARS", "power": 1800}, {"elementId": "4205", "listImage": "list_2524wattdaylite_n", "name": "2500/4000 Watt Daylite Par®", "nomalImage": "scene_mole_2524wattdaylite_n", "selectedImage": "scene_mole_2524wattdaylite_p", "statisticsImage": "statistics_2524wattdaylite_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE PARS", "power": 4000}, {"elementId": "4206", "listImage": "list_6kdaylitepar_n", "name": "6000 Watt Daylite Par®", "nomalImage": "scene_mole_6000daylitepar_n", "selectedImage": "scene_mole_6000daylitepar_p", "statisticsImage": "statistics_6kdaylitepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE PARS", "power": 6000}, {"elementId": "4207", "listImage": "list_1218kdaylitepar_n", "name": "12000/18000 Watt Daylite Par®", "nomalImage": "scene_mole_1218kdaylitepar_n", "selectedImage": "scene_mole_1218kdaylitepar_p", "statisticsImage": "statistics_1218kdaylitepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE PARS", "power": 18000}, {"elementId": "4208", "listImage": "list_650onelight_n", "name": "650 Watt Molequartz® One-Light Molefay®", "nomalImage": "scene_mole_650onelight_n", "selectedImage": "scene_mole_650onelight_p", "statisticsImage": "statistics_650onelight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE FAYS", "power": 650}, {"elementId": "4209", "listImage": "list_1300twolight_n", "name": "1300 Watt Molequartz® Two-Light Molefay®", "nomalImage": "scene_mole_1300twolight_n", "selectedImage": "scene_mole_1300twolight_p", "statisticsImage": "statistics_1300twolight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE FAYS", "power": 1300}, {"elementId": "4210", "listImage": "list_2600fourlight_n", "name": "2600 Watt Molequartz® Four-Light Molefay®", "nomalImage": "scene_mole_2600fourlight_n", "selectedImage": "scene_mole_2600fourlight_p", "statisticsImage": "statistics_2600fourlight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE FAYS", "power": 2600}, {"elementId": "4211", "listImage": "list_3900sixlight_n", "name": "3900 Watt Molequartz® Six-Light Molefay®", "nomalImage": "scene_mole_3900sixlight_n", "selectedImage": "scene_mole_3900sixlight_p", "statisticsImage": "statistics_3900sixlight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE FAYS", "power": 3900}, {"elementId": "4212", "listImage": "list_5850ninelight_n", "name": "5850 Watt Molequartz® Nine-Light Molefay®", "nomalImage": "scene_mole_5850ninelight_n", "selectedImage": "scene_mole_5850ninelight_p", "statisticsImage": "statistics_5850ninelight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE FAYS", "power": 5850}, {"elementId": "4213", "listImage": "list_7800twelvelight_n", "name": "7800 Watt Molequartz® Twelve-Light Molefay®", "nomalImage": "scene_mole_7800twelvelight_n", "selectedImage": "scene_mole_7800twelvelight_p", "statisticsImage": "statistics_7800twelvelight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE FAYS", "power": 7800}, {"elementId": "4214", "listImage": "list_1000quartzmolepar_n", "name": "1000 Watt Molequartz® Molepar®", "nomalImage": "scene_mole_1000molepar_n", "selectedImage": "scene_mole_1000molepar_p", "statisticsImage": "statistics_1000quartzmolepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE PARS", "power": 1000}, {"elementId": "4215", "listImage": "list_6ksixlightmolepar_n", "name": "6000 Watt Molequartz® Six-Light Molepar®", "nomalImage": "scene_mole_6000sixlightmolepar_n", "selectedImage": "scene_mole_6000sixlightmolepar_p", "statisticsImage": "statistics_6ksixlightmolepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE PARS", "power": 6000}, {"elementId": "4216", "listImage": "list_9kninelightmolepar_n", "name": "9000 Watt Molequartz® Nine-Light Molepar®", "nomalImage": "scene_mole_9kninelightmolepar_n", "selectedImage": "scene_mole_9kninelightmolepar_p", "statisticsImage": "statistics_9kninelightmolepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLE PARS", "power": 9000}, {"elementId": "4217", "listImage": "list_12kmoleenomolepar_n", "name": "12000 Watt Molequartz® \"Moleeno\"Molepar®", "nomalImage": "scene_mole_12k<PERSON><PERSON><PERSON>_n", "selectedImage": "scene_mole_12k<PERSON><PERSON><PERSON>_p", "statisticsImage": "statistics_12kmoleenomolepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLEENOS", "power": 12000}, {"elementId": "4218", "listImage": "list_24kmoleenomolepar_n", "name": "24000 Watt Molequartz® \"Moleeno\"Molepar®", "nomalImage": "scene_mole_24k<PERSON><PERSON><PERSON>_n", "selectedImage": "scene_mole_24k<PERSON><PERSON><PERSON>_p", "statisticsImage": "statistics_24kmoleenomolepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLEENOS", "power": 24000}, {"elementId": "4219", "listImage": "list_36kmoleenomolepar_n", "name": "36000 Watt Molequartz® \"Moleeno\"Molepar®", "nomalImage": "scene_mole_36k<PERSON><PERSON><PERSON>_n", "selectedImage": "scene_mole_36k<PERSON><PERSON><PERSON>_p", "statisticsImage": "statistics_36kmoleenomolepar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLEENOS", "power": 36000}, {"elementId": "4220", "listImage": "list_1000moleparcan_n", "name": "1000 Watt Moleparcan®", "nomalImage": "scene_1000moleparcan_n", "selectedImage": "scene_1000moleparcan_p", "statisticsImage": "statistics_1000moleparcan_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLEPARCAN", "power": 1000}, {"elementId": "4221", "listImage": "list_2kmolebeamprojector_n", "name": "2000 Watt 18\" Molebeam Projector", "nomalImage": "scene_2000molebeampro_n", "selectedImage": "scene_2000molebeampro_p", "statisticsImage": "statistics_2kmolebeamprojector_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "TUNGSTEN MOLEBEAMS", "power": 2000}, {"elementId": "4222", "listImage": "list_5kmolebeamprojector_n", "name": "5000 Watt 24\" Molebeam Projector", "nomalImage": "scene_5000molebeampro_n", "selectedImage": "scene_5000molebeampro_p", "statisticsImage": "statistics_5kmolebeamprojector_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "TUNGSTEN MOLEBEAMS", "power": 5000}, {"elementId": "4223", "listImage": "list_10kmolebeamprojector_n", "name": "10000 Watt 24\" Molebeam Projector", "nomalImage": "scene_10000molebeampro_n", "selectedImage": "scene_10000molebeampro_p", "statisticsImage": "statistics_10kmolebeamprojector_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "TUNGSTEN MOLEBEAMS", "power": 10000}, {"elementId": "4224", "listImage": "list_20kmolebeamprojector_n", "name": "20000 Watt 36\" Molebeam Projector", "nomalImage": "scene_20000molebeampro_n", "selectedImage": "scene_20000molebeampro_p", "statisticsImage": "statistics_20kmolebeamprojector_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "TUNGSTEN MOLEBEAMS", "power": 20000}, {"elementId": "4225", "listImage": "list_1200dayliteprojector_n", "name": "1200 Watt 18\" Molebeam Daylite Projector", "nomalImage": "scene_1200molebeamdaylite_n", "selectedImage": "scene_1200molebeamdaylite_p", "statisticsImage": "statistics_1200dayliteprojector_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE MOLEBEAMS", "power": 1200}, {"elementId": "4226", "listImage": "list_24dayliteprojector_n", "name": "2500/4000 Watt 24\" Molebeam Daylite Projector", "nomalImage": "scene_254000moledaylite_n", "selectedImage": "scene_254000moledaylite_p", "statisticsImage": "statistics_24dayliteprojector_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE MOLEBEAMS", "power": 4000}, {"elementId": "4227", "listImage": "list_12kdayliteprojector_n", "name": "12000 Watt 36\" Molebeam Daylite Projector", "nomalImage": "scene_12000moledaylite_n", "selectedImage": "scene_12000moledaylite_p", "statisticsImage": "statistics_12kdayliteprojector_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "DAYLITE MOLEBEAMS", "power": 12000}, {"elementId": "4228", "listImage": "list_1000spacelite_n", "name": "1000W Molequartz® Spacelite", "nomalImage": "scene_1000spacelite_n", "selectedImage": "scene_1000spacelite_p", "statisticsImage": "statistics_1000spacelite_n", "type": 3, "combinationType": 2, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SPACELITES", "power": 1000}, {"elementId": "4229", "listImage": "list_2000spacelite_n", "name": "2000W Molequartz® Spacelite", "nomalImage": "scene_2000spacelite_n", "selectedImage": "scene_2000spacelite_p", "statisticsImage": "statistics_2000spacelite_n", "type": 3, "combinationType": 2, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SPACELITES", "power": 2000}, {"elementId": "4230", "listImage": "list_6000spacelite_n", "name": "6000W Molequartz® Spacelite", "nomalImage": "scene_6000spacelite_n", "selectedImage": "scene_6000spacelite_p", "statisticsImage": "statistics_6000spacelite_n", "type": 3, "combinationType": 2, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SPACELITES", "power": 6000}, {"elementId": "4231", "listImage": "list_12000spacelite_n", "name": "12000W Molequartz® Spacelite", "nomalImage": "scene_12000spacelite_n", "selectedImage": "scene_12000spacelite_p", "statisticsImage": "statistics_12000spacelite_n", "type": 3, "combinationType": 2, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SPACELITES", "power": 12000}, {"elementId": "4232", "listImage": "list_5000skypan_n", "name": "5000 Watt Skypan", "nomalImage": "scene_5000skypan_n", "selectedImage": "scene_5000skypan_p", "statisticsImage": "statistics_5000skypan_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SKYPANS", "power": 5000}, {"elementId": "4233", "listImage": "list_10000skypan_n", "name": "10000 Watt Skypan", "nomalImage": "scene_10000skypan_n", "selectedImage": "scene_10000skypan_p", "statisticsImage": "statistics_10000skypan_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SKYPANS", "power": 10000}, {"elementId": "4234", "listImage": "list_20000skypan_n", "name": "20000 Watt Skypan", "nomalImage": "scene_20000skypan_n", "selectedImage": "scene_20000skypan_p", "statisticsImage": "statistics_20000skypan_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SKYPANS", "power": 20000}, {"elementId": "4235", "listImage": "list_600teenieweenie_n", "name": "600 Watt Molequartz® Teenie-<PERSON><PERSON><PERSON>", "nomalImage": "scene_mole_600teenieweenie_n", "selectedImage": "scene_mole_600<PERSON>ieweenie_p", "statisticsImage": "statistics_600teenieweenie_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "OPEN FACE", "power": 600}, {"elementId": "4236", "listImage": "list_650teeniemole_n", "name": "650 Watt Molequartz® <PERSON><PERSON>", "nomalImage": "scene_mole_650<PERSON>ie<PERSON>le_n", "selectedImage": "scene_mole_650<PERSON><PERSON><PERSON><PERSON>_p", "statisticsImage": "statistics_650teeniemole_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "OPEN FACE", "power": 800}, {"elementId": "4237", "listImage": "list_1000mickeymole_n", "name": "1000 Watt Molequartz® Mickey-Mole", "nomalImage": "scene_mole_1000mickeymole_n", "selectedImage": "scene_mole_1000mickeymole_p", "statisticsImage": "statistics_1000mickeymole_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "OPEN FACE", "power": 1000}, {"elementId": "4238", "listImage": "list_2000mightymole_n", "name": "2000 Watt Molequartz® Mighty-Mole", "nomalImage": "scene_mole_2000mightymole_n", "selectedImage": "scene_mole_2000mighty<PERSON><PERSON>_p", "statisticsImage": "statistics_2000mightymole_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "OPEN FACE", "power": 2000}, {"elementId": "4239", "listImage": "list_650minisoftlite_n", "name": "650 Watt Molequartz® Mini-Softlite", "nomalImage": "scene_mole_650minisoftlite_n", "selectedImage": "scene_mole_650minisoftlite_p", "statisticsImage": "statistics_650minisoftlite_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SOFTLITES", "power": 650}, {"elementId": "4240", "listImage": "list_1000babysoftlite_n", "name": "1000 Watt Molequartz® Baby-Softlite", "nomalImage": "scene_mole_1000babysoftlite_n", "selectedImage": "scene_mole_1000babysoftlite_p", "statisticsImage": "statistics_1000babysoftlite_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SOFTLITES", "power": 1000}, {"elementId": "4241", "listImage": "list_2000babyzipsoftlite_n", "name": "2000 Watt Molequartz® Baby \"Zip\"Softlite", "nomalImage": "scene_mole_2000babyzipsoftlite_n", "selectedImage": "scene_mole_2000babyzipsoftlite_p", "statisticsImage": "statistics_2000babyzipsoftlite_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SOFTLITES", "power": 2000}, {"elementId": "4242", "listImage": "list_4000baby4ksoftlite_n", "name": "4000 Watt Molequartz® Baby 4K Softlite", "nomalImage": "scene_mole_4000baby4ksoftlite_n", "selectedImage": "scene_mole_4000baby4ksoftlite_p", "statisticsImage": "statistics_4000baby4ksoftlite_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SOFTLITES", "power": 4000}, {"elementId": "4244", "listImage": "list_2000supersoftlite_n", "name": "2000 Watt Molequartz® Super-Softlite", "nomalImage": "scene_mole_2kbsupersoftlite_n", "selectedImage": "scene_mole_2kbsupersoftlite_p", "statisticsImage": "statistics_2000supersoftlite_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SOFTLITES", "power": 2000}, {"elementId": "4246", "listImage": "list_650onelightnooklite_n", "name": "650 Watt Molequartz® One-Light Nooklite", "nomalImage": "scene_mole_650onelightnooklite_n", "selectedImage": "scene_mole_650onelightnooklite_p", "statisticsImage": "statistics_650onelightnooklite_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "NOOKLITES", "power": 650}, {"elementId": "4247", "listImage": "list_1000nooklite_n", "name": "1000 Watt Molequartz® Nooklite", "nomalImage": "scene_mole_1000nooklite_n", "selectedImage": "scene_mole_1000nooklite_p", "statisticsImage": "statistics_1000nooklite_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "NOOKLITES", "power": 1000}, {"elementId": "4248", "listImage": "list_2000nooklite_n", "name": "2000 Watt Molequartz® Nooklite", "nomalImage": "scene_mole_2000nooklite_n", "selectedImage": "scene_mole_2000nooklite_p", "statisticsImage": "statistics_2000nooklite_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "NOOKLITES", "power": 2000}, {"elementId": "4249", "listImage": "list_1000molelipso_n", "name": "1000 Watt Molequartz® Molelipso®", "nomalImage": "scene_1000molelipso_n", "selectedImage": "scene_1000molelipso_p", "statisticsImage": "statistics_1000molelipso_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLELIPSOS", "power": 1000}, {"elementId": "4250", "listImage": "list_2000molelipso_n", "name": "2000 Watt Molequartz® Molelipso®", "nomalImage": "scene_2000molelipso_n", "selectedImage": "scene_2000molelipso_p", "statisticsImage": "statistics_2000molelipso_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "MOLELIPSOS", "power": 2000}, {"elementId": "4251", "listImage": "list_imagel40_n", "name": "Image L40 LED DMX", "nomalImage": "scene_kino_miagel40_n", "selectedImage": "scene_kino_miagel40_p", "statisticsImage": "statistics_imagel40_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "KINO FLO", "lightingSubClass": "LED - Bulid-in Controller", "power": 400}, {"elementId": "4252", "listImage": "list_1000supersoftlite_n", "name": "1000 Watt Molequartz® Super-Softlite", "nomalImage": "scene_mole_1kbsupersoftlite_n", "selectedImage": "scene_mole_1kbsupersoftlite_p", "statisticsImage": "statistics_1000supersoftlite_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "<PERSON><PERSON>", "lightingSubClass": "SOFTLITES", "power": 4000}, {"elementId": "4022", "listImage": "list_tungstenfresnel_n", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "tungsten_fresnel_n", "selectedImage": "tungsten_fresnel_p", "statisticsImage": "stastistics_tungstenfresnel_n", "type": 3, "combinationType": 5, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4023", "listImage": "list_tungstenpar_n", "name": "<PERSON>ngsten Par", "nomalImage": "tungsten_par_n", "selectedImage": "tungsten_par_p", "statisticsImage": "statistics_tungstenpar_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4024", "listImage": "list_tsoftlight_n", "name": "Tungsten Softlight", "nomalImage": "tungsten_softlight_n", "selectedImage": "tungsten_softlight_p", "statisticsImage": "statistics_tsoftlight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4025", "listImage": "list_openfacelight_n", "name": "Open Face Light", "nomalImage": "tungsten_openface_n", "selectedImage": "tungsten_openface_p", "statisticsImage": "stastistics_topenface_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4026", "listImage": "list_pararray_n", "name": "Tungsten Par Array", "nomalImage": "tungsten_pararray_n", "selectedImage": "tungsten_pararray_p", "statisticsImage": "statistics_pararrays_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4027", "listImage": "list_primefixture_n", "name": "Prime Fixture", "nomalImage": "tungsten_primefixtures_n", "selectedImage": "tungsten_primefixtures_p", "statisticsImage": "statistics_tprimefixtures_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4028", "listImage": "list_spotlight_n", "name": "Spotlight", "nomalImage": "tungsten_spoltlight_n", "selectedImage": "tungsten_spoltlight_p", "statisticsImage": "statistics_spoltlight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4029", "listImage": "list_chickencoop_n", "name": "Chicken Coop", "nomalImage": "tungsten_chickencoop_n", "selectedImage": "tungsten_chickencoop_p", "statisticsImage": "statistics_chickencoop_n", "type": 3, "combinationType": 2, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4030", "listImage": "list_spacelight_n", "name": "Space Light", "nomalImage": "tungsten_spacelight_n", "selectedImage": "tungsten_spacelight_p", "statisticsImage": "statistics_spacelight_n", "type": 3, "combinationType": 2, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4031", "listImage": "list_cycstrip_n", "name": "CYC Strip", "nomalImage": "tungsten_cycstrip_n", "selectedImage": "tungsten_cycstrip_p", "statisticsImage": "statistics_cycstrip_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4032", "listImage": "list_skypan_n", "name": "Skypan", "nomalImage": "tungsten_skypan_n", "selectedImage": "tungsten_skypan_p", "statisticsImage": "statistics_tskypan_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4033", "listImage": "list_mr16lamp_n", "name": "MR16 Lamp", "nomalImage": "tungsten_mr16_n", "selectedImage": "tungsten_mr16_p", "statisticsImage": "statistics_mr16_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4034", "listImage": "list_dedolight_n", "name": "Dedolight", "nomalImage": "tungsten_dedolight_n", "selectedImage": "tungsten_dedolight_p", "statisticsImage": "statistics_dedolight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "<PERSON><PERSON><PERSON>", "power": ""}, {"elementId": "4035", "listImage": "list_hmipar_n", "name": "HMI Par", "nomalImage": "hmi_par_n", "selectedImage": "hmi_par_p", "statisticsImage": "statistics_hmipar_n", "type": 3, "combinationType": 6, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "HMI", "power": ""}, {"elementId": "4036", "listImage": "list_hmifresnel_n", "name": "HMI Fresnel", "nomalImage": "hmi_fresnel_n", "selectedImage": "hmi_fresnel_p", "statisticsImage": "statistics_hmifresnel_n", "type": 3, "combinationType": 6, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "HMI", "power": ""}, {"elementId": "4037", "listImage": "list_hmiopemfacelight_n", "name": "HMI Open Face Light", "nomalImage": "hmi_openface_n", "selectedImage": "hmi_openface_p", "statisticsImage": "statistics_hmiopenface_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "HMI", "power": ""}, {"elementId": "4038", "listImage": "list_kinoflo_n", "name": "<PERSON><PERSON>", "nomalImage": "fluorescent_kinoflo_n", "selectedImage": "fluorescent_kinoflo_p", "statisticsImage": "statistics_kinoflo_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "Fluorescent Lights", "power": ""}, {"elementId": "4039", "listImage": "list_ledpanel_n", "name": "LED Panel", "nomalImage": "led_panel_n", "selectedImage": "led_panel_p", "statisticsImage": "statistics_ledpanel_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "LED", "power": ""}, {"elementId": "4040", "listImage": "list_ledsinglesource_n", "name": "LED Single Source", "nomalImage": "led_singlesource_n", "selectedImage": "led_singlesource_p", "statisticsImage": "statistics_singlesource_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "LED", "power": ""}, {"elementId": "4041", "listImage": "list_ledtubelight_n", "name": "LED Tube Light", "nomalImage": "led_tubelight_n", "selectedImage": "led_tubelight_p", "statisticsImage": "statistics_tubelight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "LED", "power": ""}, {"elementId": "4042", "listImage": "list_ledflexible_n", "name": "LED Flexible", "nomalImage": "led_flexible_n", "selectedImage": "led_flexible_p", "statisticsImage": "statistics_flexible_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "LED", "power": ""}, {"elementId": "4043", "listImage": "list_miniledlight_n", "name": "Mini LED Light", "nomalImage": "led_minilight_n", "selectedImage": "led_minilight_p", "statisticsImage": "statistics_minilight_n", "type": 3, "combinationType": 0, "is_batterry": "0", "lightingClass": "General Lighting", "lightingSubClass": "LED", "power": ""}, {"elementId": "4253", "listImage": "list_cob60d_n", "name": "amaran COB 60d", "nomalImage": "alight_cob60d_n", "selectedImage": "alight_cob60d_p", "statisticsImage": "statistics_cob60d_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran COB Series", "power": 76}, {"elementId": "4254", "listImage": "list_cob60x_n", "name": "amaran COB 60x", "nomalImage": "alight_cob60x_n", "selectedImage": "alight_cob60x_p", "statisticsImage": "statistics_cob60x_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran COB Series", "power": 76}, {"elementId": "4255", "listImage": "list_p60x_n", "name": "amaran P60x", "nomalImage": "alight_p60x_n", "selectedImage": "alight_p60x_p", "statisticsImage": "statistics_p60x_n", "type": 3, "combinationType": 7, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran P Series", "power": 78}, {"elementId": "4256", "listImage": "list_p60c_n", "name": "amaran P60c", "nomalImage": "alight_p60c_n", "selectedImage": "alight_p60c_p", "statisticsImage": "statistics_p60c_n", "type": 3, "combinationType": 9, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran P Series", "power": 78}, {"elementId": "4257", "listImage": "list_novap600c_n", "name": "Nova P600c", "nomalImage": "alight_novap600c_n", "selectedImage": "alight_novap600c_p", "statisticsImage": "statistics_novap600c_n", "type": 3, "combinationType": 8, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Nova", "power": 720}, {"elementId": "4258", "listImage": "list_ls600xpro_n", "name": "LS 600x Pro", "nomalImage": "alight_ls600xpro_n", "selectedImage": "alight_ls600xpro_p", "statisticsImage": "statistics_ls600xpro_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 720}, {"elementId": "4259", "listImage": "list_ls1200dpro_n", "name": "LS 1200d Pro", "nomalImage": "alight_ls1200dpro_n", "selectedImage": "alight_ls1200dpro_p", "statisticsImage": "statistics_ls1200dpro_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 1440}, {"elementId": "4260", "listImage": "list_f21c_n", "name": "amaran F21c", "nomalImage": "scene_amaranf21c_n", "selectedImage": "scene_amaranf21c_p", "statisticsImage": "statistics_amaranf21c_n", "type": 3, "combinationType": 10, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran F Series", "power": 120}, {"elementId": "4261", "listImage": "list_f21x_n", "name": "amaran F21x", "nomalImage": "scene_amaranf21x_n", "selectedImage": "scene_amaranf21x_p", "statisticsImage": "statistics_amaranf21x_n", "type": 3, "combinationType": 10, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran F Series", "power": 120}, {"elementId": "4262", "listImage": "list_f22c_n", "name": "amaran F22c", "nomalImage": "scene_amaranf22c_n", "selectedImage": "scene_amaranf22c_p", "statisticsImage": "statistics_amaranf22c_n", "type": 3, "combinationType": 11, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran F Series", "power": 240}, {"elementId": "4263", "listImage": "list_f22x_n", "name": "amaran F22x", "nomalImage": "scene_amaranf22x_n", "selectedImage": "scene_amaranf22x_p", "statisticsImage": "statistics_amaranf22x_n", "type": 3, "combinationType": 11, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran F Series", "power": 240}, {"elementId": "4264", "listImage": "list_t2c_n", "name": "T2c", "nomalImage": "scene_amarant2c_n", "selectedImage": "scene_amarant2c_p", "statisticsImage": "statistics_amarant2c_n", "type": 3, "combinationType": 12, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran T Series", "power": 25}, {"elementId": "4265", "listImage": "list_t4c_n", "name": "T4c", "nomalImage": "scene_amarant4c_n", "selectedImage": "scene_amarant4c_p", "statisticsImage": "statistics_amarant4c_n", "type": 3, "combinationType": 13, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran T Series", "power": 50}, {"elementId": "4266", "listImage": "list_ls600d_n", "name": "LS 600d", "nomalImage": "alight_ls600d_n", "selectedImage": "alight_ls600d_p", "statisticsImage": "statistics_ls600d_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 720}, {"elementId": "4267", "listImage": "list_ls600cpro_n", "name": "LS 600c Pro", "nomalImage": "alight_ls600cpro_n", "selectedImage": "alight_ls600cpro_p", "statisticsImage": "statistics_ls600cpro_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 720}, {"elementId": "4268", "listImage": "list_amaranpt1c_n", "name": "amaran PT1c", "nomalImage": "alight_pt1c_n", "selectedImage": "alight_pt1c_p", "statisticsImage": "statistics_pt1c_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "amaran", "lightingSubClass": "amaran PT Series", "power": 8}, {"elementId": "4269", "listImage": "list_amaranpt2c_n", "name": "amaran PT2c", "nomalImage": "alight_pt2c_n", "selectedImage": "alight_pt2c_p", "statisticsImage": "statistics_pt2c_n", "type": 3, "combinationType": 12, "is_batterry": "1", "lightingClass": "amaran", "lightingSubClass": "amaran PT Series", "power": 16}, {"elementId": "4270", "listImage": "list_amaranpt4c_n", "name": "amaran PT4c", "nomalImage": "alight_pt4c_n", "selectedImage": "alight_pt4c_p", "statisticsImage": "statistics_pt4c_n", "type": 3, "combinationType": 13, "is_batterry": "1", "lightingClass": "amaran", "lightingSubClass": "amaran PT Series", "power": 32}, {"elementId": "4271", "listImage": "list_amaran150c_n", "name": "amaran 150c", "nomalImage": "alight_amaran_150c_n", "selectedImage": "alight_amaran_150c_p", "statisticsImage": "statistics_amaran150c_n", "type": 3, "combinationType": 14, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran 150 Series", "power": 180}, {"elementId": "4272", "listImage": "list_amaran300c_n", "name": "amaran 300c", "nomalImage": "alight_amaran_300c_n", "selectedImage": "alight_amaran_300c_p", "statisticsImage": "statistics_amaran300c_n", "type": 3, "combinationType": 14, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran 300 Series", "power": 360}, {"elementId": "4273", "listImage": "list_mtpro_n", "name": "MT Pro", "nomalImage": "scene_mtpro_n", "selectedImage": "scene_mtpro_p", "statisticsImage": "statistics_mtpro_n", "type": 3, "combinationType": 15, "is_batterry": "1", "lightingClass": "Aputure", "lightingSubClass": "Series Mini", "power": 9}, {"elementId": "4274", "listImage": "list_almcpro_n", "name": "MC Pro", "nomalImage": "scene_almcpro_n", "selectedImage": "scene_almcpro_p", "statisticsImage": "statistics_almcpro_n", "type": 3, "combinationType": 20, "is_batterry": "1", "lightingClass": "Aputure", "lightingSubClass": "Series Mini", "power": 7}, {"elementId": "4275", "listImage": "list_infinibarspb3_n", "name": "INFINIBAR PB3", "nomalImage": "scene_pb3_n", "selectedImage": "scene_pb3_p", "statisticsImage": "statistics_infinibarspb3_n", "type": 3, "combinationType": 16, "is_batterry": "1", "lightingClass": "Aputure", "lightingSubClass": "INFINIBAR", "power": 9}, {"elementId": "4276", "listImage": "list_infinibarspb6_n", "name": "INFINIBAR PB6", "nomalImage": "scene_pb6_n", "selectedImage": "scene_pb6_p", "statisticsImage": "statistics_infinibarspb6_n", "type": 3, "combinationType": 17, "is_batterry": "1", "lightingClass": "Aputure", "lightingSubClass": "INFINIBAR", "power": 18}, {"elementId": "4277", "listImage": "list_infinibarspb12_n", "name": "INFINIBAR PB12", "nomalImage": "scene_pb12_n", "selectedImage": "scene_pb12_p", "statisticsImage": "statistics_infinibarspb12_n", "type": 3, "combinationType": 18, "is_batterry": "1", "lightingClass": "Aputure", "lightingSubClass": "INFINIBAR", "power": 36}, {"elementId": "4278", "listImage": "list_amaran100ds_n", "name": "amaran 100d S", "nomalImage": "alight_amaran_100ds_n", "selectedImage": "alight_amaran_100ds_p", "statisticsImage": "statistics_amaran100ds_n", "type": 3, "combinationType": 19, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran S Series", "power": 111}, {"elementId": "4279", "listImage": "list_amaran100xs_n", "name": "amaran 100x S", "nomalImage": "alight_amaran_100xs_n", "selectedImage": "alight_amaran_100xs_p", "statisticsImage": "statistics_amaran100xs_n", "type": 3, "combinationType": 19, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran S Series", "power": 113}, {"elementId": "4280", "listImage": "list_amaran200ds_n", "name": "amaran 200d S", "nomalImage": "alight_amaran_200ds_n", "selectedImage": "alight_amaran_200ds_p", "statisticsImage": "statistics_amaran200ds_n", "type": 3, "combinationType": 19, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran S Series", "power": 221}, {"elementId": "4281", "listImage": "list_amaran200xs_n", "name": "amaran 200x S", "nomalImage": "alight_amaran_200xs_n", "selectedImage": "alight_amaran_200xs_p", "statisticsImage": "statistics_amaran200xs_n", "type": 3, "combinationType": 19, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran S Series", "power": 229}, {"elementId": "4282", "listImage": "list_cob60d_n", "name": "amaran 60d S", "nomalImage": "alight_cob60d_n", "selectedImage": "alight_cob60d_p", "statisticsImage": "statistics_cob60d_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran S Series", "power": 77}, {"elementId": "4283", "listImage": "list_cob60x_n", "name": "amaran 60x S", "nomalImage": "alight_cob60x_n", "selectedImage": "alight_cob60x_p", "statisticsImage": "statistics_cob60x_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran S Series", "power": 78}, {"elementId": "4284", "listImage": "list_cs15_n", "name": "Electro Storm CS15", "nomalImage": "alight_cs15_n", "selectedImage": "alight_cs15_p", "statisticsImage": "statistics_cs15_n", "type": 3, "combinationType": 19, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Electro Storm", "power": 2200}, {"elementId": "4285", "listImage": "list_xt26_n", "name": "Electro Storm XT26", "nomalImage": "alight_xt26_n", "selectedImage": "alight_xt26_p", "statisticsImage": "statistics_xt26_n", "type": 3, "combinationType": 19, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Electro Storm", "power": 3500}, {"elementId": "4286", "listImage": "list_amaran200c_n", "name": "amaran 200c", "nomalImage": "alight_amaran_200c_n", "selectedImage": "alight_amaran_200c_p", "statisticsImage": "statistics_amaran200c_n", "type": 3, "combinationType": 21, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran 200 Series", "power": 240}, {"elementId": "4287", "listImage": "list_p40x_n", "name": "amaran P40x", "nomalImage": "alight_p40x_n", "selectedImage": "alight_p40x_p", "statisticsImage": "statistics_p40x_n", "type": 3, "combinationType": 22, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "amaran P Series", "power": 45}, {"elementId": "4288", "listImage": "list_ls1200dpro_n", "name": "STORM 1200x", "nomalImage": "alight_ls1200dpro_n", "selectedImage": "alight_ls1200dpro_p", "statisticsImage": "statistics_ls1200dpro_n", "type": 3, "combinationType": 23, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "STORM", "power": 1550}, {"elementId": "4289", "listImage": "list_ls1200dpro_n", "name": "STORM 1000c", "nomalImage": "alight_ls1200dpro_n", "selectedImage": "alight_ls1200dpro_p", "statisticsImage": "statistics_ls1200dpro_n", "type": 3, "combinationType": 23, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "STORM", "power": 1300}, {"elementId": "4290", "listImage": "list_aputure80c_n", "name": "STORM 80c", "nomalImage": "alight_aputure80c_n", "selectedImage": "alight_aputure80c_p", "statisticsImage": "statistics_aputure80c_n", "type": 3, "combinationType": 24, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "STORM", "power": 100}, {"elementId": "4291", "listImage": "list_ls600cpro_n", "name": "LS 600c Pro Ⅱ", "nomalImage": "alight_ls600cpro_n", "selectedImage": "alight_ls600cpro_p", "statisticsImage": "statistics_ls600cpro_n", "type": 3, "combinationType": 3, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "Series Lightstorm", "power": 720}, {"elementId": "4292", "listImage": "list_mr044_n", "name": "amaran Ace 25x", "nomalImage": "alight_mr044_n", "selectedImage": "alight_mr044_p", "statisticsImage": "statistics_amaranmr044_n", "type": 3, "combinationType": 25, "is_batterry": "1", "lightingClass": "amaran", "lightingSubClass": "Mini Lights", "power": 41}, {"elementId": "4293", "listImage": "list_mr044_n", "name": "amaran Ace 25c", "nomalImage": "alight_mr044_n", "selectedImage": "alight_mr044_p", "statisticsImage": "statistics_amaranmr044_n", "type": 3, "combinationType": 25, "is_batterry": "1", "lightingClass": "amaran", "lightingSubClass": "Mini Lights", "power": 41}, {"elementId": "4294", "listImage": "list_amaranmr_go_n", "name": "amaran Go", "nomalImage": "alight_amaranmr_go_n", "selectedImage": "alight_amaranmr_go_p", "statisticsImage": "statistics_amaranmr_go_n", "type": 3, "combinationType": 0, "is_batterry": "1", "lightingClass": "amaran", "lightingSubClass": "Mini Lights", "power": 2}, {"elementId": "4295", "listImage": "list_infinimat_1x2_n", "name": "INFINIMAT 1x2", "nomalImage": "alight_infinimat_1x2_n", "selectedImage": "alight_infinimat_1x2_p", "statisticsImage": "statistics_infinimat_1x2_n", "type": 3, "combinationType": 26, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "INFINIMAT", "power": 81}, {"elementId": "4296", "listImage": "list_infinimat_1x4_n", "name": "INFINIMAT 1x4", "nomalImage": "alight_infinimat_1x4_n", "selectedImage": "alight_infinimat_1x4_p", "statisticsImage": "statistics_infinimat_1x4_n", "type": 3, "combinationType": 26, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "INFINIMAT", "power": 182}, {"elementId": "4297", "listImage": "list_infinimat_2x4_n", "name": "INFINIMAT 2x4", "nomalImage": "alight_infinimat_2x4_n", "selectedImage": "alight_infinimat_2x4_p", "statisticsImage": "statistics_infinimat_2x4_n", "type": 3, "combinationType": 26, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "INFINIMAT", "power": 324}, {"elementId": "4298", "listImage": "list_infinimat_4x4_n", "name": "INFINIMAT 4x4", "nomalImage": "alight_infinimat_4x4_n", "selectedImage": "alight_infinimat_4x4_p", "statisticsImage": "statistics_infinimat_4x4_n", "type": 3, "combinationType": 26, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "INFINIMAT", "power": 640}, {"elementId": "4299", "listImage": "list_infinimat_8x8_n", "name": "INFINIMAT 8x8", "nomalImage": "alight_infinimat_8x8_n", "selectedImage": "alight_infinimat_8x8_p", "statisticsImage": "statistics_infinimat_8x8_n", "type": 3, "combinationType": 26, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "INFINIMAT", "power": 1600}, {"elementId": "4300", "listImage": "list_infinimat_20x20_n", "name": "INFINIMAT 20x20", "nomalImage": "alight_infinimat_20x20_n", "selectedImage": "alight_infinimat_20x20_p", "statisticsImage": "statistics_infinimat_20x20_n", "type": 3, "combinationType": 26, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "INFINIMAT", "power": 6400}, {"elementId": "4301", "listImage": "list_pano60c_n", "name": "Pano 60c", "nomalImage": "alight_pano60c_n", "selectedImage": "alight_pano60c_p", "statisticsImage": "statistics_pano60c_barndoor_n", "type": 3, "combinationType": 27, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "Pan<PERSON>", "power": 65}, {"elementId": "4302", "listImage": "list_pano120c_n", "name": "Pano 120c", "nomalImage": "alight_pano120c_n", "selectedImage": "alight_pano120c_p", "statisticsImage": "statistics_pano120c_barndoor_n", "type": 3, "combinationType": 28, "is_batterry": "0", "lightingClass": "amaran", "lightingSubClass": "Pan<PERSON>", "power": 140}, {"elementId": "4303", "listImage": "list_xt52_n", "name": "STORM XT52", "nomalImage": "alight_xt52_n", "selectedImage": "alight_xt52_p", "statisticsImage": "statistics_xt52_n", "type": 3, "combinationType": 29, "is_batterry": "0", "lightingClass": "Aputure", "lightingSubClass": "STORM", "power": 4800}, {"elementId": "5000", "listImage": "list_216diffusion_n", "name": "216 White Diffusion", "nomalImage": "modifier_216diffusion_n", "selectedImage": "modifier_216diffusion_p", "statisticsImage": "statistics_216diffusion_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "White Diffusion"}, {"elementId": "5001", "listImage": "list_416diffusion_n", "name": "416 Three Quarter White Diffusion", "nomalImage": "modifier_416diffusion_n", "selectedImage": "modifier_416diffusion_p", "statisticsImage": "statistics_416diffusion_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "White Diffusion"}, {"elementId": "5002", "listImage": "list_250half_n", "name": "250 Half", "nomalImage": "modifier_250half_n", "selectedImage": "modifier_250half_p", "statisticsImage": "statistics_250half_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "White Diffusion"}, {"elementId": "5003", "listImage": "list_450diffusion_n", "name": "450 Three Elghth White Diffusion", "nomalImage": "modifier_450diffusion_n", "selectedImage": "modifier_450diffusion_p", "statisticsImage": "statistics_450diffusion_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "White Diffusion"}, {"elementId": "5004", "listImage": "list_251diffusion_n", "name": "251 Quarter White Diffusion", "nomalImage": "modifier_251diffusion_n", "selectedImage": "modifier_251diffusion_p", "statisticsImage": "statistics_251diffusion_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "White Diffusion"}, {"elementId": "5005", "listImage": "list_252diffusion_n", "name": "252 Elghth White Diffusion", "nomalImage": "modifier_252diffusion_n", "selectedImage": "modifier_252diffusion_p", "statisticsImage": "statistics_252diffusion_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "White Diffusion"}, {"elementId": "5006", "listImage": "list_452diffusion_n", "name": "452 Sixteenth White Diffusion", "nomalImage": "modifier_452diffusion_n", "selectedImage": "modifier_452diffusion_p", "statisticsImage": "statistics_452diffusion_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "White Diffusion"}, {"elementId": "5007", "listImage": "list_255frost_n", "name": "255 Hollywood Frost", "nomalImage": "modifier_255frost_n", "selectedImage": "modifier_255frost_p", "statisticsImage": "statistics_255frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5008", "listImage": "list_410frost_n", "name": "410 Opal Frost", "nomalImage": "modifier_410frost_n", "selectedImage": "modifier_410frost_p", "statisticsImage": "statistics_410frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5009", "listImage": "list_420frost_n", "name": "420 Light Opal Frost", "nomalImage": "modifier_420frost_n", "selectedImage": "modifier_420frost_p", "statisticsImage": "statistics_420frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5010", "listImage": "list_258frost_n", "name": "258 Eighth <PERSON> Frost", "nomalImage": "modifier_258frost_n", "selectedImage": "modifier_258frost_p", "statisticsImage": "statistics_258frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5011", "listImage": "list_257frost_n", "name": "257 Quarter Hampshire Frost", "nomalImage": "modifier_257frost_n", "selectedImage": "modifier_257frost_p", "statisticsImage": "statistics_257frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5012", "listImage": "list_256frost_n", "name": "256 Half Hampshire Frost", "nomalImage": "modifier_256frost_n", "selectedImage": "modifier_256frost_p", "statisticsImage": "statistics_256frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5013", "listImage": "list_253frost_n", "name": "253 Hampshire Frost", "nomalImage": "modifier_253frost_n", "selectedImage": "modifier_253frost_p", "statisticsImage": "statistics_253frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5014", "listImage": "list_254frost_n", "name": "254 New Hampshire Frost", "nomalImage": "modifier_254frost_n", "selectedImage": "modifier_254frost_p", "statisticsImage": "statistics_254frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5015", "listImage": "list_750frost_n", "name": "750 Durham Frost", "nomalImage": "modifier_750frost_n", "selectedImage": "modifier_750frost_p", "statisticsImage": "statistics_750frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5016", "listImage": "list_129frost_n", "name": "129 Heavy Frost", "nomalImage": "modifier_129frost_n", "selectedImage": "modifier_129frost_p", "statisticsImage": "statistics_129frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5017", "listImage": "list_220frost_n", "name": "220 White Frost", "nomalImage": "modifier_220frost_n", "selectedImage": "modifier_220frost_p", "statisticsImage": "statistics_220frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5018", "listImage": "list_439frost_n", "name": "439 Heavy Quiet Frost", "nomalImage": "modifier_439frost_n", "selectedImage": "modifier_439frost_p", "statisticsImage": "statistics_439frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON><PERSON>"}, {"elementId": "5019", "listImage": "list_402frost_n", "name": "402 Soft Frost", "nomalImage": "modifier_402frost_n", "selectedImage": "modifier_402frost_p", "statisticsImage": "statistics_402frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON><PERSON>"}, {"elementId": "5020", "listImage": "list_429frost_n", "name": "429 <PERSON>", "nomalImage": "modifier_429frost_n", "selectedImage": "modifier_429frost_p", "statisticsImage": "statistics_429frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON><PERSON>"}, {"elementId": "5021", "listImage": "list_404frost_n", "name": "404 Half sold Frost", "nomalImage": "modifier_404frost_n", "selectedImage": "modifier_404frost_p", "statisticsImage": "statistics_404frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON><PERSON>"}, {"elementId": "5022", "listImage": "list_414frost_n", "name": "414 Highlight", "nomalImage": "modifier_414frost_n", "selectedImage": "modifier_414frost_p", "statisticsImage": "statistics_414frost_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON><PERSON>"}, {"elementId": "5023", "listImage": "list_430cloth_n", "name": "430 <PERSON><PERSON>", "nomalImage": "modifier_430cloth_n", "selectedImage": "modifier_430cloth_p", "statisticsImage": "statistics_430cloth_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5024", "listImage": "list_432cloth_n", "name": "432 <PERSON> G<PERSON>", "nomalImage": "modifier_432cloth_n", "selectedImage": "modifier_432cloth_p", "statisticsImage": "statistics_432cloth_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5025", "listImage": "list_434cloth_n", "name": "434 Quarter Grid <PERSON>h", "nomalImage": "modifier_434cloth_n", "selectedImage": "modifier_434cloth_p", "statisticsImage": "statistics_434cloth_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5026", "listImage": "list_460cloth_n", "name": "460 Quiet G<PERSON>", "nomalImage": "modifier_460cloth_n", "selectedImage": "modifier_460cloth_p", "statisticsImage": "statistics_460cloth_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5027", "listImage": "list_462cloth_n", "name": "462 Quiet Light Grid <PERSON>", "nomalImage": "modifier_462cloth_n", "selectedImage": "modifier_462cloth_p", "statisticsImage": "statistics_462cloth_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5028", "listImage": "list_464cloth_n", "name": "464 Quiet Quarter Grid <PERSON>", "nomalImage": "modifier_464cloth_n", "selectedImage": "modifier_464cloth_p", "statisticsImage": "statistics_464cloth_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON>"}, {"elementId": "5029", "listImage": "list_229spun_n", "name": "229 Quarter Tough Spun", "nomalImage": "modifier_229spun_n", "selectedImage": "modifier_229spun_p", "statisticsImage": "statistics_229spun_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Tough Spun"}, {"elementId": "5030", "listImage": "list_264spun_n", "name": "264 Tough Spun FR-3/8", "nomalImage": "modifier_264spun_n", "selectedImage": "modifier_264spun_p", "statisticsImage": "statistics_264spun_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Tough Spun"}, {"elementId": "5031", "listImage": "list_265spun_n", "name": "265 Tough Spun FR-1/4", "nomalImage": "modifier_265spun_n", "selectedImage": "modifier_265spun_p", "statisticsImage": "statistics_265spun_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Tough Spun"}, {"elementId": "5032", "listImage": "list_graygrid_n", "name": "Neutral Gray Grid", "nomalImage": "modifier_graygrid_n", "selectedImage": "modifier_graygrid_p", "statisticsImage": "statistics_graygrid_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Charcoal Vintagetm Grid And Netural Gray Grid"}, {"elementId": "5033", "listImage": "list_polysilk_n", "name": "Polysilk", "nomalImage": "modifier_polysilk_n", "selectedImage": "modifier_polysilk_p", "statisticsImage": "statistics_polysilk_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON>s"}, {"elementId": "5034", "listImage": "list_chinasilk_n", "name": "China Silk", "nomalImage": "modifier_chinasilk_n", "selectedImage": "modifier_chinasilk_p", "statisticsImage": "statistics_chinasilk_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON>s"}, {"elementId": "5035", "listImage": "list_quartersilk_n", "name": "Quarter Silk", "nomalImage": "modifier_quartersilk_n", "selectedImage": "modifier_quartersilk_p", "statisticsImage": "statistics_quartersilk_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON>s"}, {"elementId": "5036", "listImage": "list_blackpolysilk_n", "name": "Black Polysilk", "nomalImage": "modifier_blackpolysilk_n", "selectedImage": "modifier_blackpolysilk_p", "statisticsImage": "statistics_blackpolysilk_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON>s"}, {"elementId": "5037", "listImage": "list_blackchinaslik_n", "name": "Black China Silk", "nomalImage": "modifier_blackchinaslik_n", "selectedImage": "modifier_blackchinaslik_p", "statisticsImage": "statistics_blackchinaslik_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON>s"}, {"elementId": "5038", "listImage": "list_blackquarterslik_n", "name": "Black Quarter Silk", "nomalImage": "modifier_blackquarterslik_n", "selectedImage": "modifier_blackquarterslik_p", "statisticsImage": "statistics_blackquarterslik_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON>s"}, {"elementId": "5039", "listImage": "list_naturemuslin_n", "name": "Nature Muslin", "nomalImage": "modifier_naturemuslin_n", "selectedImage": "modifier_naturemuslin_p", "statisticsImage": "statistics_naturemuslin_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON><PERSON>"}, {"elementId": "5040", "listImage": "list_bleachedmuslin_n", "name": "Bleached <PERSON><PERSON>lin", "nomalImage": "modifier_bleachedmuslin_n", "selectedImage": "modifier_bleachedmuslin_p", "statisticsImage": "statistics_bleachedmuslin_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "<PERSON><PERSON><PERSON>"}, {"elementId": "5041", "listImage": "list_butterfly_n", "name": "Butterfly", "nomalImage": "modifier_butterfly_n", "selectedImage": "modifier_butterfly_p", "statisticsImage": "statistics_butterfly_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Butterfly/Overhead"}, {"elementId": "5042", "listImage": "list_magiccloth_n", "name": "Magic Cloth", "nomalImage": "modifier_magiccloth_n", "selectedImage": "modifier_magiccloth_p", "statisticsImage": "statistics_magiccloth_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Magic Cloth"}, {"elementId": "5043", "listImage": "list_ssoftbox_n", "name": "diagram_element_modifier_diffusions_softboxes_softboxes_mini", "nomalImage": "modifier_minisoftbox_n", "selectedImage": "modifier_minisoftbox_p", "statisticsImage": "statistics_ssoftbox_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Softbox"}, {"elementId": "5044", "listImage": "list_msoftbox_n", "name": "diagram_element_modifier_diffusions_softboxes_softboxes_medium", "nomalImage": "modifier_mediumsoftbox_n", "selectedImage": "modifier_mediumsoftbox_p", "statisticsImage": "statistics_msoftbox_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Softbox"}, {"elementId": "5045", "listImage": "list_lsoftbox_n", "name": "diagram_element_modifier_diffusions_softboxes_softboxes_large", "nomalImage": "modifier_largesoftbox_n", "selectedImage": "modifier_largesoftbox_p", "statisticsImage": "statistics_lsoftbox_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Softbox"}, {"elementId": "5046", "listImage": "list_sgridsoftbox_n", "name": "diagram_element_modifier_diffusions_softboxes_softboxes_mini_with_grid", "nomalImage": "modifier_minilgridsoftbox_n", "selectedImage": "modifier_minilgridsoftbox_p", "statisticsImage": "statistics_sgridsoftbox_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Softbox"}, {"elementId": "5047", "listImage": "list_mgridsoftbox_n", "name": "diagram_element_modifier_diffusions_softboxes_softboxes_medium_with_grid", "nomalImage": "modifier_mediumgridsoftbox_n", "selectedImage": "modifier_mediumgridsoftbox_p", "statisticsImage": "statistics_mgridsoftbox_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Softbox"}, {"elementId": "5048", "listImage": "list_lgridsoftbox_n", "name": "diagram_element_modifier_diffusions_softboxes_softboxes_large_with_grid", "nomalImage": "modifier_largegridsoftbox_n", "selectedImage": "modifier_largegridsoftbox_p", "statisticsImage": "statistics_lgridsoftbox_n", "type": 4, "accessoryClass": "Diffusion", "accessorySubClass": "Softbox"}, {"elementId": "5049", "listImage": "list_flags_n", "name": "Flags", "nomalImage": "modifier_flags_n", "selectedImage": "modifier_flags_p", "statisticsImage": "statistics_flags_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Flag & Cutters & Floppies"}, {"elementId": "5050", "listImage": "list_emptyframe_n", "name": "Empty Frame", "nomalImage": "modifier_emptyframe_n", "selectedImage": "modifier_emptyframe_p", "statisticsImage": "statistics_emptyframe_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Flag & Cutters & Floppies"}, {"elementId": "5051", "listImage": "list_flexscrim_n", "name": "Flex Scrim", "nomalImage": "modifier_flexscrim_n", "selectedImage": "modifier_flexscrim_p", "statisticsImage": "statistics_flexscrim_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Flag & Cutters & Floppies"}, {"elementId": "5052", "listImage": "list_wagflag_n", "name": "Wag Flags", "nomalImage": "modifier_wagflag_n", "selectedImage": "modifier_wagflag_p", "statisticsImage": "statistics_wagflag_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Flag & Cutters & Floppies"}, {"elementId": "5053", "listImage": "list_tanheat_n", "name": "Heat Cloth(Tan)", "nomalImage": "modifier_tanheat_n", "selectedImage": "modifier_tanheat_p", "statisticsImage": "statistics_tanheat_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Heat Flags & Cutters"}, {"elementId": "5054", "listImage": "list_blackheat_n", "name": "Heat Cloth(Black)", "nomalImage": "modifier_blackheat_n", "selectedImage": "modifier_blackheat_p", "statisticsImage": "statistics_blackheat_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Heat Flags & Cutters"}, {"elementId": "5055", "listImage": "list_heatfalg_n", "name": "Black Duvetyne Heat Cloth Flag", "nomalImage": "modifier_heatfalg_n", "selectedImage": "modifier_heatfalg_p", "statisticsImage": "statistics_heatfalg_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Heat Flags & Cutters"}, {"elementId": "5056", "listImage": "list_fingers_n", "name": "Finger", "nomalImage": "modifier_fingers_n", "selectedImage": "modifier_fingers_p", "statisticsImage": "statistics_fingers_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Fingers & Dots"}, {"elementId": "5057", "listImage": "list_dots_n", "name": "DOT", "nomalImage": "modifier_dots_n", "selectedImage": "modifier_dots_p", "statisticsImage": "statistics_dots_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Fingers & Dots"}, {"elementId": "5058", "listImage": "list_singlescrims_n", "name": "Single Net Scrim", "nomalImage": "modifier_singlescrim_n", "selectedImage": "modifier_singlescrim_p", "statisticsImage": "statistics_singlescrims_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Net Scrims"}, {"elementId": "5059", "listImage": "list_doublescrims_n", "name": "Double Net Scrims", "nomalImage": "modifier_doublescrim_n", "selectedImage": "modifier_doublescrim_p", "statisticsImage": "statistics_doublescrims_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Net Scrims"}, {"elementId": "5060", "listImage": "list_triplescrims_n", "name": "Triple Net Scrims", "nomalImage": "modifier_triplescrim_n", "selectedImage": "modifier_triplescrim_p", "statisticsImage": "statistics_triplescrims_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Net Scrims"}, {"elementId": "5061", "listImage": "list_whitesingle_n", "name": "White Single Net", "nomalImage": "modifier_whitesingle_n", "selectedImage": "modifier_whitesingle_p", "statisticsImage": "statistics_whitesingle_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Net Scrims"}, {"elementId": "5062", "listImage": "list_whitedouble_n", "name": "White Double Net", "nomalImage": "modifier_whitedouble_n", "selectedImage": "modifier_whitedouble_p", "statisticsImage": "statistics_whitedouble_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Net Scrims"}, {"elementId": "5063", "listImage": "list_whitetriple_n", "name": "White Triple Net", "nomalImage": "modifier_whitetriple_n", "selectedImage": "modifier_whitetriple_p", "statisticsImage": "statistics_whitetriple_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Net Scrims"}, {"elementId": "5064", "listImage": "list_steelsingle_n", "name": "Stainless Steel Single Net", "nomalImage": "modifier_steelsingle_n", "selectedImage": "modifier_steelsingle_p", "statisticsImage": "statistics_steelsingle_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Net Scrims"}, {"elementId": "5065", "listImage": "list_steeldouble_n", "name": "Stainless Steel Double Net", "nomalImage": "modifier_steeldouble_n", "selectedImage": "modifier_steeldouble_p", "statisticsImage": "statistics_steeldouble_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Net Scrims"}, {"elementId": "5066", "listImage": "list_barndoorcircle_n", "name": "SingleSource Barndoor", "nomalImage": "barndoor_circle_n", "selectedImage": "barndoor_circle_p", "statisticsImage": "statistics_gbarndoor_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "BarnDoors"}, {"elementId": "5067", "listImage": "list_barndoorsquare_n", "name": "Panel Barndoor", "nomalImage": "barndoor_square_n", "selectedImage": "barndoor_square_p", "statisticsImage": "statistics_sbarndoor_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "BarnDoors"}, {"elementId": "5068", "listImage": "list_cucoloris_n", "name": "<PERSON><PERSON><PERSON><PERSON>", "nomalImage": "modifier_cucoloris_n", "selectedImage": "modifier_cucoloris_p", "statisticsImage": "statistics_cucoloris_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Cucoloris & Branchaloris"}, {"elementId": "5069", "listImage": "list_branchaloris_n", "name": "Branchalori", "nomalImage": "modifier_branchaloris_n", "selectedImage": "modifier_branchaloris_p", "statisticsImage": "statistics_branchaloris_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Cucoloris & Branchaloris"}, {"elementId": "5070", "listImage": "list_shutters_n", "name": "Shutter", "nomalImage": "modifier_shutters_n", "selectedImage": "modifier_shutters_p", "statisticsImage": "statistics_shutters_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Shutters & GELS & ND"}, {"elementId": "5071", "listImage": "list_gels_n", "name": "GEL", "nomalImage": "modifier_gels_n", "selectedImage": "modifier_gels_p", "statisticsImage": "statistics_gels_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Shutters & GELS & ND"}, {"elementId": "5072", "listImage": "list_nd_n", "name": "ND", "nomalImage": "modifier_nd_n", "selectedImage": "modifier_nd_p", "statisticsImage": "statistics_nd_n", "type": 4, "accessoryClass": "Cuts & Paterns", "accessorySubClass": "Shutters & GELS & ND"}, {"elementId": "5073", "listImage": "list_goldreflector_n", "name": "Gold Reflector", "nomalImage": "modifier_goldreflector_n", "selectedImage": "modifier_goldreflector_p", "statisticsImage": "statistics_gflector_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Bounces & Reflectors"}, {"elementId": "5074", "listImage": "list_silverreflector_n", "name": "Silver Reflector", "nomalImage": "modifier_silverreflector_n", "selectedImage": "modifier_silverreflector_p", "statisticsImage": "statistics_sreflector_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Bounces & Reflectors"}, {"elementId": "5075", "listImage": "list_mirrorreflector_n", "name": "Mirror Reflector", "nomalImage": "modifier_mirrorreflector_n", "selectedImage": "modifier_mirrorreflector_p", "statisticsImage": "statistics_mreflector_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Bounces & Reflectors"}, {"elementId": "5076", "listImage": "list_beadboard_n", "name": "Bead board", "nomalImage": "modifier_beadboard_n", "selectedImage": "modifier_beadboard_p", "statisticsImage": "statistics_beadboard_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Bounces & Reflectors"}, {"elementId": "5077", "listImage": "list_foamcore_n", "name": "Foam-core", "nomalImage": "modifier_foamcore_n", "selectedImage": "modifier_foamcore_p", "statisticsImage": "statistics_foamcore_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Bounces & Reflectors"}, {"elementId": "5078", "listImage": "list_showcard_n", "name": "Show Card", "nomalImage": "modifier_showcard_n", "selectedImage": "modifier_showcard_p", "statisticsImage": "statistics_showcard_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Bounces & Reflectors"}, {"elementId": "5079", "listImage": "list_griff_n", "name": "Griff(Griff<PERSON>n)", "nomalImage": "modifier_griff_n", "selectedImage": "modifier_griff_p", "statisticsImage": "statistics_griff_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Bounces & Reflectors"}, {"elementId": "5080", "listImage": "list_bobinette_n", "name": "Bobinette", "nomalImage": "modifier_bobinette_n", "selectedImage": "modifier_bobinette_p", "statisticsImage": "statistics_bobinette_n", "type": 4, "accessoryClass": "Others", "accessorySubClass": "Element"}, {"elementId": "5081", "listImage": "list_blackvelvet_n", "name": "Black Velvet", "nomalImage": "modifier_blackvelvet_n", "selectedImage": "modifier_blackvelvet_p", "statisticsImage": "statistics_blackvelvet_n", "type": 4, "accessoryClass": "Others", "accessorySubClass": "Element"}, {"elementId": "5082", "listImage": "list_digitalgreen_n", "name": "Digital Green", "nomalImage": "modifier_digitalgreen_n", "selectedImage": "modifier_digitalgreen_p", "statisticsImage": "statistics_digitalgreen_n", "type": 4, "accessoryClass": "Others", "accessorySubClass": "Element"}, {"elementId": "5083", "listImage": "list_grid_n", "name": "Grid", "nomalImage": "modifier_grid_n", "selectedImage": "modifier_grid_p", "statisticsImage": "statistics_grid_n", "type": 4, "accessoryClass": "Others", "accessorySubClass": "Element"}, {"elementId": "5084", "listImage": "list_fresnel2x_n", "name": "Fresnel 2X", "nomalImage": "alight_fresnel2x_n", "selectedImage": "alight_fresnel2x_p", "statisticsImage": "statistics_fresnel2x_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Spots"}, {"elementId": "5085", "listImage": "list_spotlight2_n", "name": "Spotlight", "nomalImage": "alight_spotlight2_n", "selectedImage": "alight_spotlight2_p", "statisticsImage": "statistics_spotlight2_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Spots"}, {"elementId": "5086", "listImage": "list_broadreflector_n", "name": "Broad Reflector", "nomalImage": "alight_broadreflector_n", "selectedImage": "alight_broadreflector_p", "statisticsImage": "statistics_broadreflector_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Reflectors"}, {"elementId": "5087", "listImage": "list_standardreflector_n", "name": "Standard Reflector", "nomalImage": "alight_standardreflector_n", "selectedImage": "alight_standardreflector_p", "statisticsImage": "statistics_standardreflector_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Reflectors"}, {"elementId": "5088", "listImage": "list_deepreflector_n", "name": "Deep Reflector", "nomalImage": "alight_deepreflector_n", "selectedImage": "alight_deepreflector_p", "statisticsImage": "statistics_deepreflector_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Reflectors"}, {"elementId": "5089", "listImage": "list_bevelreflector_n", "name": "Bevel board", "nomalImage": "alight_bevelreflector_n", "selectedImage": "alight_bevelreflector_p", "statisticsImage": "statistics_bevelreflector_n", "type": 4, "accessoryClass": "Spots & Bounces & Reflectors", "accessorySubClass": "Reflectors"}, {"elementId": "", "listImage": "", "name": "", "nomalImage": "", "selectedImage": "", "statisticsImage": "", "type": 4, "accessoryClass": "", "accessorySubClass": ""}, {"elementId": "6000", "listImage": "list_panavision_n", "name": "Panavision", "nomalImage": "camera_panavision_n", "selectedImage": "camera_panavision_p", "statisticsImage": "statistics_panavision_n", "type": 5}, {"elementId": "6001", "listImage": "list_arri_n", "name": "ARRI", "nomalImage": "camera_arri_n", "selectedImage": "camera_arri_p", "statisticsImage": "statistics_arri_n", "type": 5}, {"elementId": "6002", "listImage": "list_sony_n", "name": "Sony", "nomalImage": "camera_sony_n", "selectedImage": "camera_sony_p", "statisticsImage": "statistics_sony_n", "type": 5}, {"elementId": "6003", "listImage": "list_panasonic_n", "name": "Panasonic", "nomalImage": "camera_panasonic_n", "selectedImage": "camera_panasonic_p", "statisticsImage": "statistics_panasonic_n", "type": 5}, {"elementId": "6004", "listImage": "list_red_n", "name": "RED", "nomalImage": "camera_red_n", "selectedImage": "camera_red_p", "statisticsImage": "statistics_red_n", "type": 5}, {"elementId": "6005", "listImage": "list_fuji_n", "name": "FujiFilm", "nomalImage": "camera_fuji_n", "selectedImage": "camera_fuji_p", "statisticsImage": "statistics_fuji_n", "type": 5}, {"elementId": "6006", "listImage": "list_canon_n", "name": "Canon", "nomalImage": "camera_canon_n", "selectedImage": "camera_canon_p", "statisticsImage": "statistics_canon_n", "type": 5}, {"elementId": "6007", "listImage": "list_nikon_n", "name": "Nikon", "nomalImage": "camera_nikon_n", "selectedImage": "camera_nikon_p", "statisticsImage": "statistics_nikon_n", "type": 5}, {"elementId": "6008", "listImage": "list_dji_n", "name": "DJI", "nomalImage": "camera_dji_n", "selectedImage": "camera_dji_p", "statisticsImage": "statistics_dji_n", "type": 5}, {"elementId": "6009", "listImage": "list_kinefinity_n", "name": "Kinefinity", "nomalImage": "camera_kinefinity_n", "selectedImage": "camera_kinefinity_p", "statisticsImage": "statistics_kinefinity_n", "type": 5}, {"elementId": "7000", "listImage": "list_minibaby_n", "name": "Mini-baby Stand", "nomalImage": "stand_minibabystand_n", "selectedImage": "stand_minibabystand_p", "statisticsImage": "statistics_minibabystand_n", "type": 6}, {"elementId": "7001", "listImage": "list_babystand_n", "name": "Baby Stand", "nomalImage": "stand_babystand_n", "selectedImage": "stand_babystand_p", "statisticsImage": "statistics_babystand_n", "type": 6}, {"elementId": "7002", "listImage": "list_juniorstand_n", "name": "Junior Stand", "nomalImage": "stand_juniorstand_n", "selectedImage": "stand_juniorstand_p", "statisticsImage": "statistics_juniorstand_n", "type": 6}, {"elementId": "7003", "listImage": "list_combostand_n", "name": "Combo Stand", "nomalImage": "stand_combostand_n", "selectedImage": "stand_combostand_p", "statisticsImage": "statistics_combostand_n", "type": 6}, {"elementId": "7004", "listImage": "list_lowboy_n", "name": "Low Boy", "nomalImage": "stand_lowboy_n", "selectedImage": "stand_lowboy_p", "statisticsImage": "statistics_lowboy_n", "type": 6}, {"elementId": "7005", "listImage": "list_turtlestand_n", "name": "Turtle Stand", "nomalImage": "stand_turtlestand_n", "selectedImage": "stand_turtlestand_p", "statisticsImage": "statistics_turtlestand_n", "type": 6}, {"elementId": "7006", "listImage": "list_tbone_n", "name": "T-bone", "nomalImage": "stand_tbone_n", "selectedImage": "stand_tbone_p", "statisticsImage": "statistics_tbone_n", "type": 6}, {"elementId": "7007", "listImage": "list_mombocombo_n", "name": "Mombo Combo", "nomalImage": "stand_mombocomo_n", "selectedImage": "stand_mombocomo_p", "statisticsImage": "statistics_mombocomo_n", "type": 6}, {"elementId": "7008", "listImage": "list_crankup_n", "name": "Crank-up Stand", "nomalImage": "stand_crankupstand_n", "selectedImage": "stand_crankupstand_p", "statisticsImage": "statistics_crankupstand_n", "type": 6}, {"elementId": "7009", "listImage": "list_motorized_n", "name": "Motorized Stand", "nomalImage": "stand_motorizedstand_n", "selectedImage": "stand_motorizedstand_p", "statisticsImage": "statistics_motorizedstand_n", "type": 6}, {"elementId": "7010", "listImage": "list_cstand_n", "name": "C-Stand", "nomalImage": "stand_cstand_n", "selectedImage": "stand_cstand_p", "statisticsImage": "statistics_cstand_n", "type": 6}, {"elementId": "7011", "listImage": "list_mediumroller_n", "name": "Medium Roller", "nomalImage": "stand_mediumroller_n", "selectedImage": "stand_mediumroller_p", "statisticsImage": "statistics_mediumroller_n", "type": 6}, {"elementId": "7012", "listImage": "list_hiroller_n", "name": "<PERSON> <PERSON>", "nomalImage": "stand_hiroller_n", "selectedImage": "stand_hiroller_p", "statisticsImage": "statistics_hiroller_n", "type": 6}, {"elementId": "7013", "listImage": "list_hihiroller_n", "name": "Hi-Hi Roller", "nomalImage": "stand_hihiroller_n", "selectedImage": "stand_hihiroller_p", "statisticsImage": "statistics_hihiroller_n", "type": 6}, {"elementId": "7014", "listImage": "stand_without_n", "name": "Without", "nomalImage": "stand_without_n", "selectedImage": "stand_without_p", "statisticsImage": "", "type": 6}, {"elementId": "8000", "listImage": "list_gridclamp_n", "name": "<PERSON><PERSON>", "nomalImage": "rigging_gridclamp_n", "selectedImage": "rigging_gridclamp_p", "statisticsImage": "statistics_gridclamp_n", "type": 7}, {"elementId": "8001", "listImage": "list_furniture_n", "name": "Furniture Clamp", "nomalImage": "rigging_furniture_n", "selectedImage": "rigging_furniture_p", "statisticsImage": "statistics_furniture_n", "type": 7}, {"elementId": "8002", "listImage": "list_setwallmount_n", "name": "Set Wall Mount", "nomalImage": "rigging_wallmount_n", "selectedImage": "rigging_wallmount_p", "statisticsImage": "statistics_wallmount_n", "type": 7}, {"elementId": "8003", "listImage": "list_cclamp_n", "name": "C-Clamp", "nomalImage": "rigging_cclamp_n", "selectedImage": "rigging_cclamp_p", "statisticsImage": "statistics_cclamp_n", "type": 7}, {"elementId": "8004", "listImage": "list_barclamp_n", "name": "Bar Clamp", "nomalImage": "rigging_barclamp_n", "selectedImage": "rigging_barclamp_p", "statisticsImage": "statistics_barclamp_n", "type": 7}, {"elementId": "8005", "listImage": "list_gaffergrip_n", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "rigging_gaffergrip_n", "selectedImage": "rigging_gaffergrip_p", "statisticsImage": "statistics_gaffergrip_n", "type": 7}, {"elementId": "8006", "listImage": "list_vicegrip_n", "name": "<PERSON> Grip", "nomalImage": "rigging_vicegrip_n", "selectedImage": "rigging_vicegrip_p", "statisticsImage": "statistics_vicegrip_n", "type": 7}, {"elementId": "8007", "listImage": "list_chainvice_n", "name": "Chain Vice Grip", "nomalImage": "rigging_chainvice_n", "selectedImage": "rigging_chainvice_p", "statisticsImage": "statistics_chainvice_n", "type": 7}, {"elementId": "8008", "listImage": "list_nailonplate_n", "name": "Nail-<PERSON>e", "nomalImage": "rigging_nailonplate_n", "selectedImage": "rigging_nailonplate_p", "statisticsImage": "statistics_nailonplate_n", "type": 7}, {"elementId": "8009", "listImage": "list_mafer_n", "name": "<PERSON><PERSON>", "nomalImage": "rigging_mafer_n", "selectedImage": "rigging_mafer_p", "statisticsImage": "statistics_mafer_n", "type": 7}, {"elementId": "8010", "listImage": "list_wallspreader_n", "name": "<PERSON>er", "nomalImage": "rigging_wallspreader_n", "selectedImage": "rigging_wallspreader_p", "statisticsImage": "statistics_wallspreader_n", "type": 7}, {"elementId": "8011", "listImage": "list_pipeclamp_n", "name": "<PERSON><PERSON>", "nomalImage": "rigging_pipeclamp_n", "selectedImage": "rigging_pipeclamp_p", "statisticsImage": "statistics_pipeclamp_n", "type": 7}, {"elementId": "8012", "listImage": "list_couplers_n", "name": "Couplers", "nomalImage": "rigging_couplers_n", "selectedImage": "rigging_couplers_p", "statisticsImage": "statistics_couplers", "type": 7}, {"elementId": "8013", "listImage": "list_tubestretcher_n", "name": "<PERSON><PERSON>", "nomalImage": "rigging_tubestretcher_n", "selectedImage": "rigging_tubestretcher_p", "statisticsImage": "statistics_tubestretcher_n", "type": 7}, {"elementId": "8014", "listImage": "list_megaclaw_n", "name": "Mega Claw", "nomalImage": "rigging_megaclaw_n", "selectedImage": "rigging_megaclaw_p", "statisticsImage": "statistics_megaclaw_n", "type": 7}, {"elementId": "8015", "listImage": "list_matthpole_n", "name": "<PERSON><PERSON>", "nomalImage": "rigging_matthpole_n", "selectedImage": "rigging_matthpole_p", "statisticsImage": "statistics_matthpole_n", "type": 7}, {"elementId": "8016", "listImage": "list_stirruphanger_n", "name": "Telescoping Stirrup Hanger", "nomalImage": "rigging_stirruphanger_n", "selectedImage": "rigging_stirruphanger_p", "statisticsImage": "statistics_stirruphanger_n", "type": 7}, {"elementId": "8017", "listImage": "list_suctiongrip_n", "name": "Suction Grip", "nomalImage": "rigging_suctiongrip_n", "selectedImage": "rigging_suctiongrip_p", "statisticsImage": "statistics_suctiongrip_n", "type": 7}, {"elementId": "8018", "listImage": "list_scissorclip_n", "name": "<PERSON><PERSON>or Clip", "nomalImage": "rigging_scissorclip_n", "selectedImage": "rigging_scissorclip_p", "statisticsImage": "statistics_scissorclip_n", "type": 7}, {"elementId": "8019", "listImage": "list_trapeze_n", "name": "Trapeze", "nomalImage": "rigging_trapeze_n", "selectedImage": "rigging_trapeze_p", "statisticsImage": "statistics_trapeze_n", "type": 7}, {"elementId": "8020", "listImage": "list_puttyknife_n", "name": "Putty Knife", "nomalImage": "rigging_puttyknife_n", "selectedImage": "rigging_puttyknife_p", "statisticsImage": "statistics_puttyknife_n", "type": 7}, {"elementId": "8021", "listImage": "stand_without_n", "name": "Without", "nomalImage": "rigging_without_n", "selectedImage": "rigging_without_p", "statisticsImage": "", "type": 7}, {"elementId": "9024", "name": "Nova P300c SoftBox", "nomalImage": "asoftbox_n", "selectedImage": "asoftbox_p", "statisticsImage": "statistics_apanelsoftbox_n", "type": 8, "combinationType": 1}, {"elementId": "9025", "name": "Nova P300c SoftBox with Grid", "nomalImage": "softboxgrid_n", "selectedImage": "softboxgrid_p", "statisticsImage": "statistics_agridsoftbox_n", "type": 8, "combinationType": 1}, {"elementId": "9026", "name": "Nova P300c Barn Doors", "nomalImage": "abarndoor_n", "selectedImage": "abarndoor_p", "statisticsImage": "statistics_abarndoor_n", "type": 8, "combinationType": 1}, {"elementId": "9027", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 1}, {"elementId": "9000", "name": "Reflector", "nomalImage": "reflector_n", "selectedImage": "reflector_p", "statisticsImage": "statistics_areflector_n", "type": 8, "combinationType": 3}, {"elementId": "9001", "name": "Light Dome Mini", "nomalImage": "domemini_n", "selectedImage": "domemini_p", "statisticsImage": "statistics_adomemini_n", "type": 8, "combinationType": 3}, {"elementId": "9002", "name": "Light Dome", "nomalImage": "dome_n", "selectedImage": "dome_p", "statisticsImage": "statistics_adome_n", "type": 8, "combinationType": 3}, {"elementId": "9003", "name": "Fresnel", "nomalImage": "fresnel_n", "selectedImage": "fresnel_p", "statisticsImage": "statistics_afresnel_n", "type": 8, "combinationType": 3}, {"elementId": "9004", "name": "Fresnel 2X", "nomalImage": "fresnel2x_n", "selectedImage": "fresnel2x_p", "statisticsImage": "statistics_afresnel2x_n", "type": 8, "combinationType": 3}, {"elementId": "9005", "name": "Barndoor", "nomalImage": "gbarndoor_n", "selectedImage": "gbarndoor_p", "statisticsImage": "statistics_gbarndoor_n", "type": 8, "combinationType": 3}, {"elementId": "9006", "name": "Spotlight", "nomalImage": "spotlight_n", "selectedImage": "spotlight_p", "statisticsImage": "statistics_spotlight2_n", "type": 8, "combinationType": 3}, {"elementId": "9018", "name": "Light OctaDome 120", "nomalImage": "dome120_n", "selectedImage": "dome120_p", "statisticsImage": "statistics_dome120_n", "type": 8, "combinationType": 3}, {"elementId": "9019", "name": "Light Box 6090", "nomalImage": "lightbox6090_n", "selectedImage": "lightbox6090_p", "statisticsImage": "statistics_lightbox6090_n", "type": 8, "combinationType": 3}, {"elementId": "9020", "name": "Light Box 6090 with <PERSON>rid", "nomalImage": "lightbox6090_grid_n", "selectedImage": "lightbox6090_grid_p", "statisticsImage": "statistics_lightbox6090grid_n", "type": 8, "combinationType": 3}, {"elementId": "9021", "name": "Light Box 30120", "nomalImage": "lightbox30120_n", "selectedImage": "lightbox30120_p", "statisticsImage": "statistics_lightbox30120_n", "type": 8, "combinationType": 3}, {"elementId": "9007", "name": "Softbox", "nomalImage": "asoftbox_n", "selectedImage": "asoftbox_p", "statisticsImage": "statistics_apanelsoftbox_n", "type": 8, "combinationType": 4}, {"elementId": "9008", "name": "Softbox with Grid", "nomalImage": "softboxgrid_n", "selectedImage": "softboxgrid_p", "statisticsImage": "statistics_agridsoftbox_n", "type": 8, "combinationType": 4}, {"elementId": "9009", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 4}, {"elementId": "9010", "name": "Half Double", "nomalImage": "halfdouble_n", "selectedImage": "halfdouble_p", "statisticsImage": "statistics_halfdouble_n", "type": 8, "combinationType": 5}, {"elementId": "9011", "name": "Half Single", "nomalImage": "halfsingle_n", "selectedImage": "halfsingle_p", "statisticsImage": "statistics_halfsingle_n", "type": 8, "combinationType": 5}, {"elementId": "9012", "name": "Double", "nomalImage": "double_n", "selectedImage": "double_p", "statisticsImage": "statistics_double_n", "type": 8, "combinationType": 5}, {"elementId": "9013", "name": "Single", "nomalImage": "single_n", "selectedImage": "single_p", "statisticsImage": "statistics_single_n", "type": 8, "combinationType": 5}, {"elementId": "9014", "name": "Barn <PERSON>", "nomalImage": "gbarndoor_n", "selectedImage": "gbarndoor_p", "statisticsImage": "statistics_gbarndoor_n", "type": 8, "combinationType": 5}, {"elementId": "9015", "name": "S<PERSON>ot", "nomalImage": "snoot_n", "selectedImage": "snoot_p", "statisticsImage": "statistics_snoot_n", "type": 8, "combinationType": 5}, {"elementId": "9016", "name": "Focal Spot", "nomalImage": "focalspot_n", "selectedImage": "focalspot_p", "statisticsImage": "statistics_focalspot_n", "type": 8, "combinationType": 5}, {"elementId": "9017", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 5}, {"elementId": "9010", "name": "Half Double", "nomalImage": "halfdouble_n", "selectedImage": "halfdouble_p", "statisticsImage": "statistics_halfdouble_n", "type": 8, "combinationType": 6}, {"elementId": "9011", "name": "Half Single", "nomalImage": "halfsingle_n", "selectedImage": "halfsingle_p", "statisticsImage": "statistics_halfsingle_n", "type": 8, "combinationType": 6}, {"elementId": "9012", "name": "Double", "nomalImage": "double_n", "selectedImage": "double_p", "statisticsImage": "statistics_double_n", "type": 8, "combinationType": 6}, {"elementId": "9013", "name": "Single", "nomalImage": "single_n", "selectedImage": "single_p", "statisticsImage": "statistics_single_n", "type": 8, "combinationType": 6}, {"elementId": "9014", "name": "Barn <PERSON>", "nomalImage": "gbarndoor_n", "selectedImage": "gbarndoor_p", "statisticsImage": "statistics_gbarndoor_n", "type": 8, "combinationType": 6}, {"elementId": "9023", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 6}, {"elementId": "9031", "name": "amaran P60x SoftBox", "nomalImage": "asoftbox_n", "selectedImage": "asoftbox_p", "statisticsImage": "statistics_apanelsoftbox_n", "type": 8, "combinationType": 7}, {"elementId": "9032", "name": "amaran P60x SoftBox with Grid", "nomalImage": "softboxgrid_n", "selectedImage": "softboxgrid_p", "statisticsImage": "statistics_agridsoftbox_n", "type": 8, "combinationType": 7}, {"elementId": "9033", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 7}, {"elementId": "9034", "name": "Nova P600c SoftBox", "nomalImage": "asoftbox_n", "selectedImage": "asoftbox_p", "statisticsImage": "statistics_apanelsoftbox_n", "type": 8, "combinationType": 8}, {"elementId": "9035", "name": "Nova P600c SoftBox with Grid", "nomalImage": "softboxgrid_n", "selectedImage": "softboxgrid_p", "statisticsImage": "statistics_agridsoftbox_n", "type": 8, "combinationType": 8}, {"elementId": "9036", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 8}, {"elementId": "9028", "name": "amaran P60c SoftBox", "nomalImage": "asoftbox_n", "selectedImage": "asoftbox_p", "statisticsImage": "statistics_apanelsoftbox_n", "type": 8, "combinationType": 9}, {"elementId": "9029", "name": "amaran P60c SoftBox with Grid", "nomalImage": "softboxgrid_n", "selectedImage": "softboxgrid_p", "statisticsImage": "statistics_agridsoftbox_n", "type": 8, "combinationType": 9}, {"elementId": "9030", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 9}, {"elementId": "9037", "name": "Softbox", "nomalImage": "softbox3060_n", "selectedImage": "softbox3060_p", "statisticsImage": "statistics_softbox3060_n", "type": 8, "combinationType": 10}, {"elementId": "9038", "name": "Lantern", "nomalImage": "lantern3060_n", "selectedImage": "lantern3060_p", "statisticsImage": "statistics_lantern3060_n", "type": 8, "combinationType": 10}, {"elementId": "9039", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 10}, {"elementId": "9040", "name": "Softbox", "nomalImage": "softbox6060_n", "selectedImage": "softbox6060_p", "statisticsImage": "statistics_softbox6060_n", "type": 8, "combinationType": 11}, {"elementId": "9041", "name": "Lantern", "nomalImage": "lantern6060_n", "selectedImage": "lantern6060_p", "statisticsImage": "statistics_lantern6060_n", "type": 8, "combinationType": 11}, {"elementId": "9042", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 11}, {"elementId": "9043", "name": "Grid", "nomalImage": "grille2_n", "selectedImage": "grille2_p", "statisticsImage": "statistics_grille2_n", "type": 8, "combinationType": 12}, {"elementId": "9044", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 12}, {"elementId": "9045", "name": "Grid", "nomalImage": "grille4_n", "selectedImage": "grille4_p", "statisticsImage": "statistics_grille4_n", "type": 8, "combinationType": 13}, {"elementId": "9046", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 13}, {"elementId": "9047", "name": "Reflector", "nomalImage": "reflector_n", "selectedImage": "reflector_p", "statisticsImage": "statistics_areflector_n", "type": 8, "combinationType": 14}, {"elementId": "9048", "name": "Light Dome Mini", "nomalImage": "domemini_n", "selectedImage": "domemini_p", "statisticsImage": "statistics_adomemini_n", "type": 8, "combinationType": 14}, {"elementId": "9049", "name": "Light Dome", "nomalImage": "dome_n", "selectedImage": "dome_p", "statisticsImage": "statistics_adome_n", "type": 8, "combinationType": 14}, {"elementId": "9050", "name": "Fresnel", "nomalImage": "fresnel_n", "selectedImage": "fresnel_p", "statisticsImage": "statistics_afresnel_n", "type": 8, "combinationType": 14}, {"elementId": "9051", "name": "Fresnel 2X", "nomalImage": "fresnel2x_n", "selectedImage": "fresnel2x_p", "statisticsImage": "statistics_afresnel2x_n", "type": 8, "combinationType": 14}, {"elementId": "9052", "name": "Barndoor", "nomalImage": "gbarndoor_n", "selectedImage": "gbarndoor_p", "statisticsImage": "statistics_gbarndoor_n", "type": 8, "combinationType": 14}, {"elementId": "9053", "name": "Spotlight", "nomalImage": "spotlight_n", "selectedImage": "spotlight_p", "statisticsImage": "statistics_spotlight2_n", "type": 8, "combinationType": 14}, {"elementId": "9054", "name": "Light OctaDome 120", "nomalImage": "dome120_n", "selectedImage": "dome120_p", "statisticsImage": "statistics_dome120_n", "type": 8, "combinationType": 14}, {"elementId": "9055", "name": "Light Box 6090", "nomalImage": "lightbox6090_n", "selectedImage": "lightbox6090_p", "statisticsImage": "statistics_lightbox6090_n", "type": 8, "combinationType": 14}, {"elementId": "9056", "name": "Light Box 6090 with <PERSON>rid", "nomalImage": "lightbox6090_grid_n", "selectedImage": "lightbox6090_grid_p", "statisticsImage": "statistics_lightbox6090grid_n", "type": 8, "combinationType": 14}, {"elementId": "9057", "name": "Light Box 30120", "nomalImage": "lightbox30120_n", "selectedImage": "lightbox30120_p", "statisticsImage": "statistics_lightbox30120_n", "type": 8, "combinationType": 14}, {"elementId": "9058", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 14}, {"elementId": "9059", "name": "Grid", "nomalImage": "mtptogrid_n", "selectedImage": "mtptogrid_p", "statisticsImage": "statistics_mtprogrid_n", "type": 8, "combinationType": 15}, {"elementId": "9060", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 15}, {"elementId": "9061", "name": "Grid", "nomalImage": "pb3_grille_n", "selectedImage": "pb3_grille_p", "statisticsImage": "statistics_pb3grid_n", "type": 8, "combinationType": 16}, {"elementId": "9062", "name": "Softbox", "nomalImage": "pb3_softbox_n", "selectedImage": "pb3_softbox_p", "statisticsImage": "statistics_pb3softbox_n", "type": 8, "combinationType": 16}, {"elementId": "9063", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 16}, {"elementId": "9064", "name": "Grid", "nomalImage": "pb6_grille_n", "selectedImage": "pb6_grille_p", "statisticsImage": "statistics_pb6grid_n", "type": 8, "combinationType": 17}, {"elementId": "9065", "name": "Softbox", "nomalImage": "pb6_softbox_n", "selectedImage": "pb6_softbox_p", "statisticsImage": "statistics_pb6softbox_n", "type": 8, "combinationType": 17}, {"elementId": "9066", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 17}, {"elementId": "9067", "name": "Grid", "nomalImage": "pb12_grille_n", "selectedImage": "pb12_grille_p", "statisticsImage": "statistics_pb12grid_n", "type": 8, "combinationType": 18}, {"elementId": "9068", "name": "Softbox", "nomalImage": "pb12_softbox_n", "selectedImage": "pb12_softbox_p", "statisticsImage": "statistics_pb12softbox_n", "type": 8, "combinationType": 18}, {"elementId": "9069", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 18}, {"elementId": "9070", "name": "Reflector", "nomalImage": "reflector_n", "selectedImage": "reflector_p", "statisticsImage": "statistics_areflector_n", "type": 8, "combinationType": 19}, {"elementId": "9071", "name": "Light Dome Mini", "nomalImage": "domemini_n", "selectedImage": "domemini_p", "statisticsImage": "statistics_adomemini_n", "type": 8, "combinationType": 19}, {"elementId": "9072", "name": "Light Dome SE", "nomalImage": "dome_n", "selectedImage": "dome_p", "statisticsImage": "statistics_domese_n", "type": 8, "combinationType": 19}, {"elementId": "9073", "name": "Light Dome II", "nomalImage": "dome2_n", "selectedImage": "dome2_p", "statisticsImage": "statistics_dome2_n", "type": 8, "combinationType": 19}, {"elementId": "9074", "name": "Fresnel 2X", "nomalImage": "fresnel2x_n", "selectedImage": "fresnel2x_p", "statisticsImage": "statistics_afresnel2x_n", "type": 8, "combinationType": 19}, {"elementId": "9075", "name": "Spotlight", "nomalImage": "spotlight_n", "selectedImage": "spotlight_p", "statisticsImage": "statistics_spotlight2_n", "type": 8, "combinationType": 19}, {"elementId": "9076", "name": "Light OctaDome 120", "nomalImage": "dome120_n", "selectedImage": "dome120_p", "statisticsImage": "statistics_dome120_n", "type": 8, "combinationType": 19}, {"elementId": "9077", "name": "Light Box 6090", "nomalImage": "lightbox6090_n", "selectedImage": "lightbox6090_p", "statisticsImage": "statistics_lightbox6090_n", "type": 8, "combinationType": 19}, {"elementId": "9078", "name": "Light Box 30120", "nomalImage": "lightbox30120_n", "selectedImage": "lightbox30120_p", "statisticsImage": "statistics_lightbox30120_n", "type": 8, "combinationType": 19}, {"elementId": "9079", "name": "Lantern 90", "nomalImage": "lantern90_n", "selectedImage": "lantern90_p", "statisticsImage": "statistics_lantern90_n", "type": 8, "combinationType": 19}, {"elementId": "9080", "name": "Lantern mini", "nomalImage": "lanternmini_n", "selectedImage": "lanternmini_p", "statisticsImage": "statistics_lanternmini_n", "type": 8, "combinationType": 19}, {"elementId": "9081", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 19}, {"elementId": "9082", "name": "Nova P600c Barn Doors", "nomalImage": "abarndoor_n", "selectedImage": "abarndoor_p", "statisticsImage": "statistics_sbarndoor_n", "type": 8, "combinationType": 8}, {"elementId": "9083", "name": "Lantern 90", "nomalImage": "lantern90_n", "selectedImage": "lantern90_p", "statisticsImage": "statistics_lantern90_n", "type": 8, "combinationType": 3}, {"elementId": "9084", "name": "Lantern 65", "nomalImage": "lantern65_n", "selectedImage": "lantern65_p", "statisticsImage": "statistics_lantern65_n", "type": 8, "combinationType": 3}, {"elementId": "9085", "name": "Spotlight SE", "nomalImage": "spotlightse_n", "selectedImage": "spotlightse_p", "statisticsImage": "statistics_spotlightse_n", "type": 8, "combinationType": 3}, {"elementId": "9086", "name": "Lantern 90", "nomalImage": "lantern90_n", "selectedImage": "lantern90_p", "statisticsImage": "statistics_lantern90_n", "type": 8, "combinationType": 14}, {"elementId": "9087", "name": "Lantern 65", "nomalImage": "lantern65_n", "selectedImage": "lantern65_p", "statisticsImage": "statistics_lantern65_n", "type": 8, "combinationType": 14}, {"elementId": "9088", "name": "Spotlight SE", "nomalImage": "spotlightse_n", "selectedImage": "spotlightse_p", "statisticsImage": "statistics_spotlightse_n", "type": 8, "combinationType": 14}, {"elementId": "9089", "name": "Lantern 65", "nomalImage": "lantern65_n", "selectedImage": "lantern65_p", "statisticsImage": "statistics_lantern65_n", "type": 8, "combinationType": 19}, {"elementId": "9090", "name": "Spotlight SE", "nomalImage": "spotlightse_n", "selectedImage": "spotlightse_p", "statisticsImage": "statistics_spotlightse_n", "type": 8, "combinationType": 19}, {"elementId": "9091", "name": "Light Control Grids", "nomalImage": "mcpro_grid_n", "selectedImage": "mcpro_grid_p", "statisticsImage": "statistics_almcpro_grid_n", "type": 8, "combinationType": 20}, {"elementId": "9092", "name": "Dome Diffusers", "nomalImage": "mcpro_dome_n", "selectedImage": "mcpro_dome_p", "statisticsImage": "statistics_almcpro_dome_n", "type": 8, "combinationType": 20}, {"elementId": "9093", "name": "Flat Diffusers", "nomalImage": "mcpro_flat_n", "selectedImage": "mcpro_flat_p", "statisticsImage": "statistics_almcpro_flat_n", "type": 8, "combinationType": 20}, {"elementId": "9094", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 20}, {"elementId": "9095", "name": "Reflector", "nomalImage": "reflector_n", "selectedImage": "reflector_p", "statisticsImage": "statistics_areflector_n", "type": 8, "combinationType": 21}, {"elementId": "9096", "name": "Barndoor", "nomalImage": "gbarndoor_n", "selectedImage": "gbarndoor_p", "statisticsImage": "statistics_gbarndoor_n", "type": 8, "combinationType": 21}, {"elementId": "9097", "name": "Fresnel 2X", "nomalImage": "fresnel2x_n", "selectedImage": "fresnel2x_p", "statisticsImage": "statistics_afresnel2x_n", "type": 8, "combinationType": 21}, {"elementId": "9098", "name": "Light Dome SE", "nomalImage": "dome_n", "selectedImage": "dome_p", "statisticsImage": "statistics_domese_n", "type": 8, "combinationType": 21}, {"elementId": "9099", "name": "Spotlight SE", "nomalImage": "spotlightse_n", "selectedImage": "spotlightse_p", "statisticsImage": "statistics_spotlightse_n", "type": 8, "combinationType": 21}, {"elementId": "9100", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 21}, {"elementId": "9101", "name": "amaran P40x SoftBox", "nomalImage": "asoftbox_n", "selectedImage": "asoftbox_p", "statisticsImage": "statistics_apanelsoftbox_n", "type": 8, "combinationType": 22}, {"elementId": "9102", "name": "amaran P40x SoftBox with Grid", "nomalImage": "softboxgrid_n", "selectedImage": "softboxgrid_p", "statisticsImage": "statistics_agridsoftbox_n", "type": 8, "combinationType": 22}, {"elementId": "9103", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 22}, {"elementId": "9104", "name": "Reflector30", "nomalImage": "reflector_30_n", "selectedImage": "reflector_30_p", "statisticsImage": "statistics_reflector_30_n", "type": 8, "combinationType": 23}, {"elementId": "9105", "name": "Reflector15", "nomalImage": "reflector_15_n", "selectedImage": "reflector_15_p", "statisticsImage": "statistics_reflector_15_n", "type": 8, "combinationType": 23}, {"elementId": "9106", "name": "Reflector45", "nomalImage": "reflector_45_n", "selectedImage": "reflector_45_p", "statisticsImage": "statistics_reflector_45_n", "type": 8, "combinationType": 23}, {"elementId": "9107", "name": "Light Dome Ⅱ", "nomalImage": "lightdome_2_n", "selectedImage": "lightdome_2_p", "statisticsImage": "statistics_dome2_n", "type": 8, "combinationType": 23}, {"elementId": "9108", "name": "Light Dome Ⅲ", "nomalImage": "lightdome_3_n", "selectedImage": "lightdome_3_p", "statisticsImage": "statistics_dome2_n", "type": 8, "combinationType": 23}, {"elementId": "9109", "name": "Light OctaDome 120", "nomalImage": "lightdome_120_n", "selectedImage": "lightdome_120_p", "statisticsImage": "statistics_dome120_n", "type": 8, "combinationType": 23}, {"elementId": "9110", "name": "Light Dome 150", "nomalImage": "lightdome_150_n", "selectedImage": "lightdome_150_p", "statisticsImage": "statistics_lightdome_150_n", "type": 8, "combinationType": 23}, {"elementId": "9111", "name": "Light Dome SE", "nomalImage": "lightdome_se_n", "selectedImage": "lightdome_se_p", "statisticsImage": "statistics_lightdome_se_n", "type": 8, "combinationType": 23}, {"elementId": "9112", "name": "Fresnel CF12", "nomalImage": "cf12_fresnel_3_n", "selectedImage": "cf12_fresnel_3_p", "statisticsImage": "statistics_cf12_fresnel_n", "type": 8, "combinationType": 23}, {"elementId": "9113", "name": "Spotlight", "nomalImage": "spotlight_n", "selectedImage": "spotlight_p", "statisticsImage": "statistics_spotlight2_n", "type": 8, "combinationType": 23}, {"elementId": "9114", "name": "Barndoor", "nomalImage": "abarndoor_n", "selectedImage": "abarndoor_p", "statisticsImage": "statistics_gbarndoor_n", "type": 8, "combinationType": 23}, {"elementId": "9115", "name": "Lightbox6090", "nomalImage": "lightbox6090_n", "selectedImage": "lightbox6090_p", "statisticsImage": "statistics_lightbox6090_n", "type": 8, "combinationType": 23}, {"elementId": "9116", "name": "Lantern", "nomalImage": "lantern_n", "selectedImage": "lantern_p", "statisticsImage": "statistics_lantern65_n", "type": 8, "combinationType": 23}, {"elementId": "9117", "name": "Lantern90", "nomalImage": "lantern_90_n", "selectedImage": "lantern_90_p", "statisticsImage": "statistics_lantern90_n", "type": 8, "combinationType": 23}, {"elementId": "9118", "name": "Transfer Bowl", "nomalImage": "transferbowl_n", "selectedImage": "transferbowl_p", "statisticsImage": "statistics_transferbowl", "type": 8, "combinationType": 23}, {"elementId": "9119", "name": "Reflector15", "nomalImage": "reflector_15_n", "selectedImage": "reflector_15_p", "statisticsImage": "statistics_reflector_15_n", "type": 8, "combinationType": 24}, {"elementId": "9120", "name": "Diffuser", "nomalImage": "diffuser_n", "selectedImage": "diffuser_p", "statisticsImage": "statistics_diffuser", "type": 8, "combinationType": 24}, {"elementId": "9121", "name": "Barndoor", "nomalImage": "gbarndoor_n", "selectedImage": "gbarndoor_p", "statisticsImage": "statistics_gbarndoor_n", "type": 8, "combinationType": 24}, {"elementId": "9122", "name": "Fresnel", "nomalImage": "fresnel_n", "selectedImage": "fresnel_p", "statisticsImage": "statistics_afresnel_n", "type": 8, "combinationType": 24}, {"elementId": "9123", "name": "Transfer Bowl", "nomalImage": "transferbowl_n", "selectedImage": "transferbowl_p", "statisticsImage": "statistics_transferbowl", "type": 8, "combinationType": 24}, {"elementId": "9124", "name": "Handheld Bracket", "nomalImage": "handheld_bracket_n", "selectedImage": "handheld_bracket_p", "statisticsImage": "statistics_handheld_bracket", "type": 8, "combinationType": 24}, {"elementId": "9125", "name": "Light Control Grids", "nomalImage": "mcpro_grid_n", "selectedImage": "mcpro_grid_p", "statisticsImage": "statistics_almcpro_grid_n", "type": 8, "combinationType": 25}, {"elementId": "9126", "name": "Dome Diffusers", "nomalImage": "mcpro_dome_n", "selectedImage": "mcpro_dome_p", "statisticsImage": "statistics_almcpro_dome_n", "type": 8, "combinationType": 25}, {"elementId": "9127", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 25}, {"elementId": "9128", "name": "Inflatable Softbox", "nomalImage": "inflatablesoftbox_n", "selectedImage": "inflatablesoftbox_p", "statisticsImage": "statistics_inflatablesoftbox", "type": 8, "combinationType": 26}, {"elementId": "9129", "name": "Inflatable Softbox+Grid", "nomalImage": "grille_n", "selectedImage": "grille_p", "statisticsImage": "statistics_grille", "type": 8, "combinationType": 26}, {"elementId": "9130", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 26}, {"elementId": "9131", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 27}, {"elementId": "9132", "name": "Pano 60c Barndoor", "nomalImage": "pano60c_banrndoor_n", "selectedImage": "pano60c_banrndoor_p", "statisticsImage": "statistics_barndoor", "type": 8, "combinationType": 27}, {"elementId": "9133", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 28}, {"elementId": "9134", "name": "Pano 120c Barndoor", "nomalImage": "pano120c_banrndoor_n", "selectedImage": "pano120c_banrndoor_p", "statisticsImage": "statistics_barndoor", "type": 8, "combinationType": 28}, {"elementId": "9135", "name": "Without", "nomalImage": "withoutaccessories_n", "selectedImage": "withoutaccessories_p", "statisticsImage": "", "type": 8, "combinationType": 29}, {"elementId": "9136", "name": "Barndoor", "nomalImage": "barndoor_n", "selectedImage": "barndoor_p", "statisticsImage": "statistics_barndoor", "type": 8, "combinationType": 29}, {"elementId": "9137", "name": "Fresnel F16", "nomalImage": "electric_fresnel_n", "selectedImage": "electric_fresnel_p", "statisticsImage": "statistics_electric_fresnel", "type": 8, "combinationType": 29}, {"elementId": "9138", "name": "Lantern 120", "nomalImage": "lantern_120_n", "selectedImage": "lantern_120_p", "statisticsImage": "statistics_lantern120", "type": 8, "combinationType": 29}, {"elementId": "9139", "name": "Lightdome 150", "nomalImage": "lightdome_150_n", "selectedImage": "lightdome_150_p", "statisticsImage": "statistics_lightdome_150", "type": 8, "combinationType": 29}, {"elementId": "9140", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "xt52_parallel_n", "selectedImage": "xt52_parallel_p", "statisticsImage": "statistics_parallel", "type": 8, "combinationType": 29}, {"elementId": "9141", "name": "Reflector 15", "nomalImage": "reflector_15_n", "selectedImage": "reflector_15_p", "statisticsImage": "statistics_reflector_15", "type": 8, "combinationType": 29}, {"elementId": "9142", "name": "Reflector 30", "nomalImage": "reflector_30_n", "selectedImage": "reflector_30_p", "statisticsImage": "statistics_reflector_30", "type": 8, "combinationType": 29}, {"elementId": "9143", "name": "Reflector 45", "nomalImage": "reflector_45_n", "selectedImage": "reflector_45_p", "statisticsImage": "statistics_reflector_45", "type": 8, "combinationType": 29}, {"elementId": "10001", "name": "LS C120d", "nomalImage": "group_120dreflector_n", "selectedImage": "group_120dreflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4005"}, {"elementId": "10002", "name": "LS C120d", "nomalImage": "group_120ddomemini_n", "selectedImage": "group_120ddomemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4005"}, {"elementId": "10003", "name": "LS C120d", "nomalImage": "group_120ddome_n", "selectedImage": "group_120ddome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4005"}, {"elementId": "10004", "name": "LS C120d", "nomalImage": "group_120dfresnel_n", "selectedImage": "group_120dfresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4005"}, {"elementId": "10005", "name": "LS C120d", "nomalImage": "group_120dfresnel2x_n", "selectedImage": "group_120dfresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4005"}, {"elementId": "10006", "name": "LS C120d", "nomalImage": "group_120dbarndoor_n", "selectedImage": "group_120dbarndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4005"}, {"elementId": "10007", "name": "LS C120d", "nomalImage": "group_120dspotlight_n", "selectedImage": "group_120dspotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4005"}, {"elementId": "10008", "name": "LS C120t", "nomalImage": "group_120treflector_n", "selectedImage": "group_120treflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4006"}, {"elementId": "10009", "name": "LS C120t", "nomalImage": "group_120tdomemini_n", "selectedImage": "group_120tdomemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4006"}, {"elementId": "10010", "name": "LS C120t", "nomalImage": "group_120tdome_n", "selectedImage": "group_120tdome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4006"}, {"elementId": "10011", "name": "LS C120t", "nomalImage": "group_120tfresnel_n", "selectedImage": "group_120tfresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4006"}, {"elementId": "10012", "name": "LS C120t", "nomalImage": "group_120tfresnel2x_n", "selectedImage": "group_120tfresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4006"}, {"elementId": "10013", "name": "LS C120t", "nomalImage": "group_120tbarndoor_n", "selectedImage": "group_120tbarndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4006"}, {"elementId": "10014", "name": "LS C120t", "nomalImage": "group_120tspotlight_n", "selectedImage": "group_120tspotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4006"}, {"elementId": "10015", "name": "LS C300d", "nomalImage": "group_300dreflector_n", "selectedImage": "group_300dreflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4004"}, {"elementId": "10016", "name": "LS C300d", "nomalImage": "group_300ddomemini_n", "selectedImage": "group_300ddomemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4004"}, {"elementId": "10017", "name": "LS C300d", "nomalImage": "group_300ddome_n", "selectedImage": "group_300ddome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4004"}, {"elementId": "10018", "name": "LS C300d", "nomalImage": "group_300dfresnel_n", "selectedImage": "group_300dfresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4004"}, {"elementId": "10019", "name": "LS C300d", "nomalImage": "group_300dfresnel2x_n", "selectedImage": "group_300dfresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4004"}, {"elementId": "10020", "name": "LS C300d", "nomalImage": "group_300dbarndoor_n", "selectedImage": "group_300dbarndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4004"}, {"elementId": "10021", "name": "LS C300d", "nomalImage": "group_300dspotlight_n", "selectedImage": "group_300dspotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4004"}, {"elementId": "10022", "name": "LS C300dII", "nomalImage": "group_300d2reflector_n", "selectedImage": "group_300d2reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4007"}, {"elementId": "10023", "name": "LS C300dII", "nomalImage": "group_300d2domemini_n", "selectedImage": "group_300d2domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4007"}, {"elementId": "10024", "name": "LS C300dII", "nomalImage": "group_300d2dome_n", "selectedImage": "group_300d2dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4007"}, {"elementId": "10025", "name": "LS C300dII", "nomalImage": "group_300d2fresnel_n", "selectedImage": "group_300d2fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4007"}, {"elementId": "10026", "name": "LS C300dII", "nomalImage": "group_300d2fresnel2x_n", "selectedImage": "group_300d2fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4007"}, {"elementId": "10027", "name": "LS C300dII", "nomalImage": "group_300d2barndoor_n", "selectedImage": "group_300d2barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4007"}, {"elementId": "10028", "name": "LS C300dII", "nomalImage": "group_300d2spotlight_n", "selectedImage": "group_300d2spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4007"}, {"elementId": "10029", "name": "LS 1s", "nomalImage": "group_ls1softbox_n", "selectedImage": "group_ls1softbox_p", "type": 9, "combinationType": 4, "accessoryId": "9007", "lightingId": "4001"}, {"elementId": "10030", "name": "LS 1s", "nomalImage": "group_ls1gridsoftbox_n", "selectedImage": "group_ls1gridsoftbox_p", "type": 9, "combinationType": 4, "accessoryId": "9008", "lightingId": "4001"}, {"elementId": "10031", "name": "LS 1c", "nomalImage": "group_ls1softbox_n", "selectedImage": "group_ls1softbox_p", "type": 9, "combinationType": 4, "accessoryId": "9007", "lightingId": "4002"}, {"elementId": "10032", "name": "LS 1c", "nomalImage": "group_ls1gridsoftbox_n", "selectedImage": "group_ls1gridsoftbox_p", "type": 9, "combinationType": 4, "accessoryId": "9008", "lightingId": "4002"}, {"elementId": "10033", "name": "HR-672w", "nomalImage": "group_672softbox_n", "selectedImage": "group_672softbox_p", "type": 9, "combinationType": 4, "accessoryId": "9007", "lightingId": "4008"}, {"elementId": "10034", "name": "HR-672w", "nomalImage": "group_672gridsoftbox_n", "selectedImage": "group_672gridsoftbox_p", "type": 9, "combinationType": 4, "accessoryId": "9008", "lightingId": "4008"}, {"elementId": "10035", "name": "HR-672s", "nomalImage": "group_672softbox_n", "selectedImage": "group_672softbox_p", "type": 9, "combinationType": 4, "accessoryId": "9007", "lightingId": "4009"}, {"elementId": "10036", "name": "HR-672s", "nomalImage": "group_672gridsoftbox_n", "selectedImage": "group_672gridsoftbox_p", "type": 9, "combinationType": 4, "accessoryId": "9008", "lightingId": "4009"}, {"elementId": "10037", "name": "HR-672c", "nomalImage": "group_672softbox_n", "selectedImage": "group_672softbox_p", "type": 9, "combinationType": 4, "accessoryId": "9007", "lightingId": "4010"}, {"elementId": "10038", "name": "HR-672c", "nomalImage": "group_672gridsoftbox_n", "selectedImage": "group_672gridsoftbox_p", "type": 9, "combinationType": 4, "accessoryId": "9008", "lightingId": "4010"}, {"elementId": "10039", "name": "Tri 8c", "nomalImage": "group_tri8softbox_n", "selectedImage": "group_tri8softbox_p", "type": 9, "combinationType": 4, "accessoryId": "9007", "lightingId": "4011"}, {"elementId": "10040", "name": "Tri 8c", "nomalImage": "group_tri8gridsoftbox_n", "selectedImage": "group_tri8gridsoftbox_p", "type": 9, "combinationType": 4, "accessoryId": "9008", "lightingId": "4011"}, {"elementId": "10041", "name": "Tri 8s", "nomalImage": "group_tri8softbox_n", "selectedImage": "group_tri8softbox_p", "type": 9, "combinationType": 4, "accessoryId": "9007", "lightingId": "4012"}, {"elementId": "10042", "name": "Tri 8s", "nomalImage": "group_tri8gridsoftbox_n", "selectedImage": "group_tri8gridsoftbox_p", "type": 9, "combinationType": 4, "accessoryId": "9008", "lightingId": "4012"}, {"elementId": "10043", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "group_tungstenfresnel_scrim_n", "selectedImage": "group_tungstenfresnel_scrim_p", "type": 9, "combinationType": 5, "accessoryId": "9010", "lightingId": "4022"}, {"elementId": "10044", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "group_tungstenfresnel_scrim_n", "selectedImage": "group_tungstenfresnel_scrim_p", "type": 9, "combinationType": 5, "accessoryId": "9011", "lightingId": "4022"}, {"elementId": "10045", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "group_tungstenfresnel_scrim_n", "selectedImage": "group_tungstenfresnel_scrim_p", "type": 9, "combinationType": 5, "accessoryId": "9012", "lightingId": "4022"}, {"elementId": "10046", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "group_tungstenfresnel_scrim_n", "selectedImage": "group_tungstenfresnel_scrim_p", "type": 9, "combinationType": 5, "accessoryId": "9013", "lightingId": "4022"}, {"elementId": "10047", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "group_tungstenfresnel_barndoor_n", "selectedImage": "group_tungstenfresnel_barndoor_p", "type": 9, "combinationType": 5, "accessoryId": "9014", "lightingId": "4022"}, {"elementId": "10048", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "group_tungstenfresnel_snoot_n", "selectedImage": "group_tungstenfresnel_snoot_p", "type": 9, "combinationType": 5, "accessoryId": "9015", "lightingId": "4022"}, {"elementId": "10049", "name": "<PERSON><PERSON><PERSON>", "nomalImage": "group_tungstenfresnel_spot_n", "selectedImage": "group_tungstenfresnel_spot_p", "type": 9, "combinationType": 5, "accessoryId": "9016", "lightingId": "4022"}, {"elementId": "10050", "name": "HMI Par", "nomalImage": "group_hmipar_scrim_n", "selectedImage": "group_hmipar_scrim_p", "type": 9, "combinationType": 6, "accessoryId": "9010", "lightingId": "4035"}, {"elementId": "10050", "name": "HMI Fresnel", "nomalImage": "group_hmifresnel_scrim_n", "selectedImage": "group_hmifresnel_scrim_p", "type": 9, "combinationType": 6, "accessoryId": "9010", "lightingId": "4036"}, {"elementId": "10051", "name": "HMI Par", "nomalImage": "group_hmipar_scrim_n", "selectedImage": "group_hmipar_scrim_p", "type": 9, "combinationType": 6, "accessoryId": "9011", "lightingId": "4035"}, {"elementId": "10051", "name": "HMI Fresnel", "nomalImage": "group_hmifresnel_scrim_n", "selectedImage": "group_hmifresnel_scrim_p", "type": 9, "combinationType": 6, "accessoryId": "9011", "lightingId": "4036"}, {"elementId": "10052", "name": "HMI Par", "nomalImage": "group_hmipar_scrim_n", "selectedImage": "group_hmipar_scrim_p", "type": 9, "combinationType": 6, "accessoryId": "9012", "lightingId": "4035"}, {"elementId": "10052", "name": "HMI Fresnel", "nomalImage": "group_hmifresnel_scrim_n", "selectedImage": "group_hmifresnel_scrim_p", "type": 9, "combinationType": 6, "accessoryId": "9012", "lightingId": "4036"}, {"elementId": "10053", "name": "HMI Par", "nomalImage": "group_hmipar_scrim_n", "selectedImage": "group_hmipar_scrim_p", "type": 9, "combinationType": 6, "accessoryId": "9013", "lightingId": "4035"}, {"elementId": "10053", "name": "HMI Fresnel", "nomalImage": "group_hmifresnel_scrim_n", "selectedImage": "group_hmifresnel_scrim_p", "type": 9, "combinationType": 6, "accessoryId": "9013", "lightingId": "4036"}, {"elementId": "10054", "name": "HMI Par", "nomalImage": "group_hmipar_fresnel_n", "selectedImage": "group_hmipar_fresnel_p", "type": 9, "combinationType": 6, "accessoryId": "9014", "lightingId": "4035"}, {"elementId": "10054", "name": "HMI Fresnel", "nomalImage": "group_hmifresnel_barndoor_n", "selectedImage": "group_hmifresnel_barndoor_p", "type": 9, "combinationType": 6, "accessoryId": "9014", "lightingId": "4036"}, {"elementId": "10055", "name": "LS C120d", "nomalImage": "group_120d_dome120_n", "selectedImage": "group_120d_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4005"}, {"elementId": "10056", "name": "LS C120d", "nomalImage": "group_120d_lightbox6090_n", "selectedImage": "group_120d_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4005"}, {"elementId": "10057", "name": "LS C120d", "nomalImage": "group_120d_lightbox6090grid_n", "selectedImage": "group_120d_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4005"}, {"elementId": "10058", "name": "LS C120d", "nomalImage": "group_120d_lightbox30120_n", "selectedImage": "group_120d_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4005"}, {"elementId": "10059", "name": "LS C300d", "nomalImage": "group_300d_dome120_n", "selectedImage": "group_300d_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4004"}, {"elementId": "10060", "name": "LS C300d", "nomalImage": "group_300d_lightbox6090_n", "selectedImage": "group_300d_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4004"}, {"elementId": "10061", "name": "LS C300d", "nomalImage": "group_300d_lightbox6090grid_n", "selectedImage": "group_300d_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4004"}, {"elementId": "10062", "name": "LS C300d", "nomalImage": "group_300d_lightbox30120_n", "selectedImage": "group_300d_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4004"}, {"elementId": "10063", "name": "LS C300dII", "nomalImage": "group_300d2_dome120_n", "selectedImage": "group_300d2_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4007"}, {"elementId": "10064", "name": "LS C300dII", "nomalImage": "group_300d2_lightbox6090_n", "selectedImage": "group_300d2_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4007"}, {"elementId": "10065", "name": "LS C300dII", "nomalImage": "group_300d2_lightbox6090grid_n", "selectedImage": "group_300d2_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4007"}, {"elementId": "10066", "name": "LS C300dII", "nomalImage": "group_300d2_lightbox30120_n", "selectedImage": "group_300d2_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4007"}, {"elementId": "10067", "name": "LS 60d", "nomalImage": "group_ls60d_softbox_n", "selectedImage": "group_ls60d_softbox_p", "type": 9, "combinationType": 4, "accessoryId": "9007", "lightingId": "4045"}, {"elementId": "10068", "name": "LS 60d", "nomalImage": "group_ls60d_softboxgrid_n", "selectedImage": "group_ls60d_softboxgrid_p", "type": 9, "combinationType": 4, "accessoryId": "9008", "lightingId": "4045"}, {"elementId": "10069", "name": "LS 60x", "nomalImage": "group_ls60x_softbox_n", "selectedImage": "group_ls60x_softbox_p", "type": 9, "combinationType": 4, "accessoryId": "9007", "lightingId": "4044"}, {"elementId": "10070", "name": "LS 60x", "nomalImage": "group_ls60x_softboxgrid_n", "selectedImage": "group_ls60x_softboxgrid_p", "type": 9, "combinationType": 4, "accessoryId": "9008", "lightingId": "4044"}, {"elementId": "10071", "name": "LS C120dII", "nomalImage": "group_120dii_reflector_n", "selectedImage": "group_120dii_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4048"}, {"elementId": "10072", "name": "LS C120dII", "nomalImage": "group_120dii_domemini_n", "selectedImage": "group_120dii_<PERSON>mini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4048"}, {"elementId": "10073", "name": "LS C120dII", "nomalImage": "group_120dii_dome_n", "selectedImage": "group_120dii_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4048"}, {"elementId": "10074", "name": "LS C120dII", "nomalImage": "group_120dii_fresnel_n", "selectedImage": "group_120dii_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4048"}, {"elementId": "10075", "name": "LS C120dII", "nomalImage": "group_120dii_fresnel2x_n", "selectedImage": "group_120dii_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4048"}, {"elementId": "10076", "name": "LS C120dII", "nomalImage": "group_120dii_barndoor_n", "selectedImage": "group_120dii_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4048"}, {"elementId": "10077", "name": "LS C120dII", "nomalImage": "group_120dii_spotlight_n", "selectedImage": "group_120dii_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4048"}, {"elementId": "10078", "name": "LS C120dII", "nomalImage": "group_120dii_dome120_n", "selectedImage": "group_120dii_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4048"}, {"elementId": "10079", "name": "LS C120dII", "nomalImage": "group_120dii_lightbox6090_n", "selectedImage": "group_120dii_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4048"}, {"elementId": "10080", "name": "LS C120dII", "nomalImage": "group_120dii_lightbox6090grid_n", "selectedImage": "group_120dii_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4048"}, {"elementId": "10081", "name": "LS C120dII", "nomalImage": "group_120dii_lightbox30120_n", "selectedImage": "group_120dii_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4048"}, {"elementId": "10082", "name": "LS 300x", "nomalImage": "group_300x_reflector_n", "selectedImage": "group_300x_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4047"}, {"elementId": "10083", "name": "LS 300x", "nomalImage": "group_300x_domemini_n", "selectedImage": "group_300x_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4047"}, {"elementId": "10084", "name": "LS 300x", "nomalImage": "group_300x_dome_n", "selectedImage": "group_300x_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4047"}, {"elementId": "10085", "name": "LS 300x", "nomalImage": "group_300x_fresnel_n", "selectedImage": "group_300x_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4047"}, {"elementId": "10086", "name": "LS 300x", "nomalImage": "group_300x_fresnel2x_n", "selectedImage": "group_300x_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4047"}, {"elementId": "10087", "name": "LS 300x", "nomalImage": "group_300x_barndoor_n", "selectedImage": "group_300x_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4047"}, {"elementId": "10088", "name": "LS 300x", "nomalImage": "group_300x_spotlight_n", "selectedImage": "group_300x_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4047"}, {"elementId": "10089", "name": "LS 300x", "nomalImage": "group_300x_dome120_n", "selectedImage": "group_300x_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4047"}, {"elementId": "10090", "name": "LS 300x", "nomalImage": "group_300x_lightbox6090_n", "selectedImage": "group_300x_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4047"}, {"elementId": "10091", "name": "LS 300x", "nomalImage": "group_300x_lightbox6090grid_n", "selectedImage": "group_300x_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4047"}, {"elementId": "10092", "name": "LS 300x", "nomalImage": "group_300x_lightbox30120_n", "selectedImage": "group_300x_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4047"}, {"elementId": "10093", "name": "LS 600d Pro", "nomalImage": "group_600d_reflector_n", "selectedImage": "group_600d_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4046"}, {"elementId": "10094", "name": "LS 600d Pro", "nomalImage": "group_600d_domemini_n", "selectedImage": "group_600d_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4046"}, {"elementId": "10095", "name": "LS 600d Pro", "nomalImage": "group_600d_dome_n", "selectedImage": "group_600d_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4046"}, {"elementId": "10096", "name": "LS 600d Pro", "nomalImage": "group_600d_fresnel_n", "selectedImage": "group_600d_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4046"}, {"elementId": "10097", "name": "LS 600d Pro", "nomalImage": "group_600d_fresnel2x_n", "selectedImage": "group_600d_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4046"}, {"elementId": "10098", "name": "LS 600d Pro", "nomalImage": "group_600d_barndoor_n", "selectedImage": "group_600d_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4046"}, {"elementId": "10099", "name": "LS 600d Pro", "nomalImage": "group_600d_spotlight_n", "selectedImage": "group_600d_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4046"}, {"elementId": "10100", "name": "LS 600d Pro", "nomalImage": "group_600d_dome120_n", "selectedImage": "group_600d_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4046"}, {"elementId": "10101", "name": "LS 600d Pro", "nomalImage": "group_600d_lightbox6090_n", "selectedImage": "group_600d_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4046"}, {"elementId": "10102", "name": "LS 600d Pro", "nomalImage": "group_600d_lightbox6090grid_n", "selectedImage": "group_600d_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4046"}, {"elementId": "10103", "name": "LS 600d Pro", "nomalImage": "group_600d_lightbox30120_n", "selectedImage": "group_600d_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4046"}, {"elementId": "10104", "name": "LS 600x Pro", "nomalImage": "group_600x_reflector_n", "selectedImage": "group_600x_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4258"}, {"elementId": "10105", "name": "LS 600x Pro", "nomalImage": "group_600x_domemini_n", "selectedImage": "group_600x_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4258"}, {"elementId": "10106", "name": "LS 600x Pro", "nomalImage": "group_600x_dome_n", "selectedImage": "group_600x_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4258"}, {"elementId": "10107", "name": "LS 600x Pro", "nomalImage": "group_600x_fresnel_n", "selectedImage": "group_600x_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4258"}, {"elementId": "10108", "name": "LS 600x Pro", "nomalImage": "group_600x_fresnel2x_n", "selectedImage": "group_600x_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4258"}, {"elementId": "10109", "name": "LS 600x Pro", "nomalImage": "group_600x_barndoor_n", "selectedImage": "group_600x_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4258"}, {"elementId": "10110", "name": "LS 600x Pro", "nomalImage": "group_600x_spotlight_n", "selectedImage": "group_600x_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4258"}, {"elementId": "10111", "name": "LS 600x Pro", "nomalImage": "group_600x_dome120_n", "selectedImage": "group_600x_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4258"}, {"elementId": "10112", "name": "LS 600x Pro", "nomalImage": "group_600x_lightbox6090_n", "selectedImage": "group_600x_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4258"}, {"elementId": "10113", "name": "LS 600x Pro", "nomalImage": "group_600x_lightbox6090grid_n", "selectedImage": "group_600x_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4258"}, {"elementId": "10114", "name": "LS 600x Pro", "nomalImage": "group_600x_lightbox30120_n", "selectedImage": "group_600x_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4258"}, {"elementId": "10115", "name": "LS C120t", "nomalImage": "group_120t_dome120_n", "selectedImage": "group_120t_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4006"}, {"elementId": "10116", "name": "LS C120t", "nomalImage": "group_120t_lightbox6090_n", "selectedImage": "group_120t_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4006"}, {"elementId": "10117", "name": "LS C120t", "nomalImage": "group_120t_lightbox6090grid_n", "selectedImage": "group_120t_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4006"}, {"elementId": "10118", "name": "LS C120t", "nomalImage": "group_120t_lightbox30120_n", "selectedImage": "group_120t_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4006"}, {"elementId": "10119", "name": "Nova P300c", "nomalImage": "group_novap300c_softbox_n", "selectedImage": "group_novap300c_softbox_p", "type": 9, "combinationType": 1, "accessoryId": "9024", "lightingId": "4000"}, {"elementId": "10120", "name": "Nova P300c", "nomalImage": "group_novap300c_softboxgrid_n", "selectedImage": "group_novap300c_softboxgrid_p", "type": 9, "combinationType": 1, "accessoryId": "9025", "lightingId": "4000"}, {"elementId": "10121", "name": "Nova P300c", "nomalImage": "group_novap300c_barndoor_n", "selectedImage": "group_novap300c_barndoor_p", "type": 9, "combinationType": 1, "accessoryId": "9026", "lightingId": "4000"}, {"elementId": "10122", "name": "amaran COB 60x", "nomalImage": "group_cob60x_reflector_n", "selectedImage": "group_cob60x_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4254"}, {"elementId": "10123", "name": "amaran COB 60x", "nomalImage": "group_cob60x_domemini_n", "selectedImage": "group_cob60x_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4254"}, {"elementId": "10124", "name": "amaran COB 60x", "nomalImage": "group_cob60x_dome_n", "selectedImage": "group_cob60x_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4254"}, {"elementId": "10125", "name": "amaran COB 60x", "nomalImage": "group_cob60x_fresnel_n", "selectedImage": "group_cob60x_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4254"}, {"elementId": "10126", "name": "amaran COB 60x", "nomalImage": "group_cob60x_fresnel2x_n", "selectedImage": "group_cob60x_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4254"}, {"elementId": "10127", "name": "amaran COB 60x", "nomalImage": "group_cob60x_barndoor_n", "selectedImage": "group_cob60x_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4254"}, {"elementId": "10128", "name": "amaran COB 60x", "nomalImage": "group_cob60x_spotlight_n", "selectedImage": "group_cob60x_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4254"}, {"elementId": "10129", "name": "amaran COB 60x", "nomalImage": "group_cob60x_dome120_n", "selectedImage": "group_cob60x_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4254"}, {"elementId": "10130", "name": "amaran COB 60x", "nomalImage": "group_cob60x_lightbox6090_n", "selectedImage": "group_cob60x_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4254"}, {"elementId": "10131", "name": "amaran COB 60x", "nomalImage": "group_cob60x_lightbox6090grid_n", "selectedImage": "group_cob60x_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4254"}, {"elementId": "10132", "name": "amaran COB 60x", "nomalImage": "group_cob60x_lightbox30120_n", "selectedImage": "group_cob60x_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4254"}, {"elementId": "10133", "name": "amaran COB 60d", "nomalImage": "group_cob60d_reflector_n", "selectedImage": "group_cob60d_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4253"}, {"elementId": "10134", "name": "amaran COB 60d", "nomalImage": "group_cob60d_domemini_n", "selectedImage": "group_cob60d_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4253"}, {"elementId": "10135", "name": "amaran COB 60d", "nomalImage": "group_cob60d_dome_n", "selectedImage": "group_cob60d_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4253"}, {"elementId": "10136", "name": "amaran COB 60d", "nomalImage": "group_cob60d_fresnel_n", "selectedImage": "group_cob60d_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4253"}, {"elementId": "10137", "name": "amaran COB 60d", "nomalImage": "group_cob60d_fresnel2x_n", "selectedImage": "group_cob60d_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4253"}, {"elementId": "10138", "name": "amaran COB 60d", "nomalImage": "group_cob60d_barndoor_n", "selectedImage": "group_cob60d_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4253"}, {"elementId": "10139", "name": "amaran COB 60d", "nomalImage": "group_cob60d_spotlight_n", "selectedImage": "group_cob60d_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4253"}, {"elementId": "10140", "name": "amaran COB 60d", "nomalImage": "group_cob60d_dome120_n", "selectedImage": "group_cob60d_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4253"}, {"elementId": "10141", "name": "amaran COB 60d", "nomalImage": "group_cob60d_lightbox6090_n", "selectedImage": "group_cob60d_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4253"}, {"elementId": "10142", "name": "amaran COB 60d", "nomalImage": "group_cob60d_lightbox6090grid_n", "selectedImage": "group_cob60d_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4253"}, {"elementId": "10143", "name": "amaran COB 60d", "nomalImage": "group_cob60d_lightbox30120_n", "selectedImage": "group_cob60d_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4253"}, {"elementId": "10144", "name": "amaran P60c", "nomalImage": "group_amaranp60c_softbox_n", "selectedImage": "group_amaranp60c_softbox_p", "type": 9, "combinationType": 9, "accessoryId": "9028", "lightingId": "4256"}, {"elementId": "10145", "name": "amaran P60c", "nomalImage": "group_amaranp60c_softboxgrid_n", "selectedImage": "group_amaranp60c_softboxgrid_p", "type": 9, "combinationType": 9, "accessoryId": "9029", "lightingId": "4256"}, {"elementId": "10146", "name": "amaran P60c", "nomalImage": "alight_p60c_n", "selectedImage": "alight_p60c_p", "type": 9, "combinationType": 9, "accessoryId": "9030", "lightingId": "4256"}, {"elementId": "10147", "name": "amaran P60x", "nomalImage": "group_amaranp60x_softbox_n", "selectedImage": "group_amaranp60x_softbox_p", "type": 9, "combinationType": 7, "accessoryId": "9031", "lightingId": "4255"}, {"elementId": "10148", "name": "amaran P60x", "nomalImage": "group_amaranp60x_softboxgrid_n", "selectedImage": "group_amaranp60x_softboxgrid_p", "type": 9, "combinationType": 7, "accessoryId": "9032", "lightingId": "4255"}, {"elementId": "10149", "name": "amaran P60x", "nomalImage": "alight_p60x_n", "selectedImage": "alight_p60x_p", "type": 9, "combinationType": 7, "accessoryId": "9033", "lightingId": "4255"}, {"elementId": "10150", "name": "Nova P600c", "nomalImage": "group_novap600c_softbox_n", "selectedImage": "group_novap600c_softbox_p", "type": 9, "combinationType": 8, "accessoryId": "9034", "lightingId": "4257"}, {"elementId": "10151", "name": "Nova P600c", "nomalImage": "group_novap600c_softboxgrid_n", "selectedImage": "group_novap600c_softboxgrid_p", "type": 9, "combinationType": 8, "accessoryId": "9035", "lightingId": "4257"}, {"elementId": "10152", "name": "Nova P600c", "nomalImage": "alight_novap600c_n", "selectedImage": "alight_novap600c_p", "type": 9, "combinationType": 8, "accessoryId": "9036", "lightingId": "4257"}, {"elementId": "10153", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_reflector_n", "selectedImage": "group_ls1200dpro_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4259"}, {"elementId": "10154", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_domemini_n", "selectedImage": "group_ls1200dpro_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4259"}, {"elementId": "10155", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_dome_n", "selectedImage": "group_ls1200dpro_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4259"}, {"elementId": "10156", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_fresnel_n", "selectedImage": "group_ls1200dpro_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4259"}, {"elementId": "10157", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_fresnel2x_n", "selectedImage": "group_ls1200dpro_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4259"}, {"elementId": "10158", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_barndoor_n", "selectedImage": "group_ls1200dpro_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4259"}, {"elementId": "10159", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_spotlight_n", "selectedImage": "group_ls1200dpro_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4259"}, {"elementId": "10160", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_dome120_n", "selectedImage": "group_ls1200dpro_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4259"}, {"elementId": "10161", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_lightbox6090_n", "selectedImage": "group_ls1200dpro_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4259"}, {"elementId": "10162", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_lightbox6090grid_n", "selectedImage": "group_ls1200dpro_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4259"}, {"elementId": "10163", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_lightbox30120_n", "selectedImage": "group_ls1200dpro_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4259"}, {"elementId": "10164", "name": "amaran F21c", "nomalImage": "group_amaranf21c_softbox_n", "selectedImage": "group_amaranf21c_softbox_p", "type": 9, "combinationType": 10, "accessoryId": "9037", "lightingId": "4260"}, {"elementId": "10165", "name": "amaran F21c", "nomalImage": "group_amaranf21c_lantern_n", "selectedImage": "group_amaranf21c_lantern_p", "type": 9, "combinationType": 10, "accessoryId": "9038", "lightingId": "4260"}, {"elementId": "10166", "name": "amaran F21c", "nomalImage": "scene_amaranf21c_n", "selectedImage": "scene_amaranf21c_p", "type": 9, "combinationType": 10, "accessoryId": "9039", "lightingId": "4260"}, {"elementId": "10167", "name": "amaran F21x", "nomalImage": "group_amaranf21x_softbox_n", "selectedImage": "group_amaranf21x_softbox_p", "type": 9, "combinationType": 10, "accessoryId": "9037", "lightingId": "4261"}, {"elementId": "10168", "name": "amaran F21x", "nomalImage": "group_amaranf21x_lantern_n", "selectedImage": "group_amaranf21x_lantern_p", "type": 9, "combinationType": 10, "accessoryId": "9038", "lightingId": "4261"}, {"elementId": "10169", "name": "amaran F21x", "nomalImage": "scene_amaranf21x_n", "selectedImage": "scene_amaranf21x_n", "type": 9, "combinationType": 10, "accessoryId": "9039", "lightingId": "4261"}, {"elementId": "10170", "name": "amaran F22c", "nomalImage": "group_amaranf22c_softbox_n", "selectedImage": "group_amaranf22c_softbox_p", "type": 9, "combinationType": 11, "accessoryId": "9040", "lightingId": "4262"}, {"elementId": "10171", "name": "amaran F22c", "nomalImage": "group_amaranf22c_lantern_n", "selectedImage": "group_amaranf22c_lantern_p", "type": 9, "combinationType": 11, "accessoryId": "9041", "lightingId": "4262"}, {"elementId": "10172", "name": "amaran F22c", "nomalImage": "scene_amaranf22c_n", "selectedImage": "scene_amaranf22c_p", "type": 9, "combinationType": 11, "accessoryId": "9042", "lightingId": "4262"}, {"elementId": "10173", "name": "amaran F22x", "nomalImage": "group_amaranf22x_softbox_n", "selectedImage": "group_amaranf22x_softbox_p", "type": 9, "combinationType": 11, "accessoryId": "9040", "lightingId": "4263"}, {"elementId": "10174", "name": "amaran F22x", "nomalImage": "group_amaranf22x_lantern_n", "selectedImage": "group_amaranf22x_lantern_p", "type": 9, "combinationType": 11, "accessoryId": "9041", "lightingId": "4263"}, {"elementId": "10175", "name": "amaran F22x", "nomalImage": "scene_amaranf22x_n", "selectedImage": "scene_amaranf22x_p", "type": 9, "combinationType": 11, "accessoryId": "9042", "lightingId": "4263"}, {"elementId": "10176", "name": "T2c", "nomalImage": "group_amarant2c_grille_n", "selectedImage": "group_amarant2c_grille_p", "type": 9, "combinationType": 12, "accessoryId": "9043", "lightingId": "4264"}, {"elementId": "10177", "name": "T2c", "nomalImage": "scene_amarant2c_n", "selectedImage": "scene_amarant2c_p", "type": 9, "combinationType": 12, "accessoryId": "9044", "lightingId": "4264"}, {"elementId": "10178", "name": "T4c", "nomalImage": "group_amarant4c_grille_n", "selectedImage": "group_amarant4c_grille_p", "type": 9, "combinationType": 13, "accessoryId": "9045", "lightingId": "4265"}, {"elementId": "10179", "name": "T4c", "nomalImage": "scene_amarant4c_n", "selectedImage": "scene_amarant4c_p", "type": 9, "combinationType": 13, "accessoryId": "9046", "lightingId": "4265"}, {"elementId": "10180", "name": "amaran 100d", "nomalImage": "alight_amaran_100d_n", "selectedImage": "alight_amaran_100d_p", "type": 9, "combinationType": 14, "accessoryId": "9058", "lightingId": "4050"}, {"elementId": "10181", "name": "amaran 100d", "nomalImage": "group_amaran100d_barndoor_n", "selectedImage": "group_amaran100d_barndoor_p", "type": 9, "combinationType": 14, "accessoryId": "9052", "lightingId": "4050"}, {"elementId": "10182", "name": "amaran 100d", "nomalImage": "group_amaran100d_dome_n", "selectedImage": "group_amaran100d_dome_p", "type": 9, "combinationType": 14, "accessoryId": "9049", "lightingId": "4050"}, {"elementId": "10183", "name": "amaran 100d", "nomalImage": "group_amaran100d_dome120_n", "selectedImage": "group_amaran100d_dome120_p", "type": 9, "combinationType": 14, "accessoryId": "9054", "lightingId": "4050"}, {"elementId": "10184", "name": "amaran 100d", "nomalImage": "group_amaran100d_domemini_n", "selectedImage": "group_amaran100d_domemini_p", "type": 9, "combinationType": 14, "accessoryId": "9048", "lightingId": "4050"}, {"elementId": "10185", "name": "amaran 100d", "nomalImage": "group_amaran100d_fresnel_n", "selectedImage": "group_amaran100d_fresnel_p", "type": 9, "combinationType": 14, "accessoryId": "9050", "lightingId": "4050"}, {"elementId": "10186", "name": "amaran 100d", "nomalImage": "group_amaran100d_fresnel2x_n", "selectedImage": "group_amaran100d_fresnel2x_p", "type": 9, "combinationType": 14, "accessoryId": "9051", "lightingId": "4050"}, {"elementId": "10188", "name": "amaran 100d", "nomalImage": "group_amaran100d_spotlight_n", "selectedImage": "group_amaran100d_spotlight_p", "type": 9, "combinationType": 14, "accessoryId": "9053", "lightingId": "4050"}, {"elementId": "10189", "name": "amaran 100d", "nomalImage": "group_amaran100d_reflector_n", "selectedImage": "group_amaran100d_reflector_p", "type": 9, "combinationType": 14, "accessoryId": "9047", "lightingId": "4050"}, {"elementId": "10190", "name": "amaran 100x", "nomalImage": "alight_amaran_100x_n", "selectedImage": "alight_amaran_100x_p", "type": 9, "combinationType": 14, "accessoryId": "9058", "lightingId": "4049"}, {"elementId": "10191", "name": "amaran 100x", "nomalImage": "group_amaran100x_barndoor_n", "selectedImage": "group_amaran100x_barndoor_p", "type": 9, "combinationType": 14, "accessoryId": "9052", "lightingId": "4049"}, {"elementId": "10192", "name": "amaran 100x", "nomalImage": "group_amaran100x_dome_n", "selectedImage": "group_amaran100x_dome_p", "type": 9, "combinationType": 14, "accessoryId": "9049", "lightingId": "4049"}, {"elementId": "10193", "name": "amaran 100x", "nomalImage": "group_amaran100x_dome120_n", "selectedImage": "group_amaran100x_dome120_p", "type": 9, "combinationType": 14, "accessoryId": "9054", "lightingId": "4049"}, {"elementId": "10194", "name": "amaran 100x", "nomalImage": "group_amaran100x_domemini_n", "selectedImage": "group_amaran100x_domemini_p", "type": 9, "combinationType": 14, "accessoryId": "9048", "lightingId": "4049"}, {"elementId": "10195", "name": "amaran 100x", "nomalImage": "group_amaran100x_fresnel_n", "selectedImage": "group_amaran100x_fresnel_p", "type": 9, "combinationType": 14, "accessoryId": "9050", "lightingId": "4049"}, {"elementId": "10196", "name": "amaran 100x", "nomalImage": "group_amaran100x_fresnel2x_n", "selectedImage": "group_amaran100x_fresnel2x_p", "type": 9, "combinationType": 14, "accessoryId": "9051", "lightingId": "4049"}, {"elementId": "10197", "name": "amaran 100x", "nomalImage": "group_amaran100x_spotlight_n", "selectedImage": "group_amaran100x_spotlight_p", "type": 9, "combinationType": 14, "accessoryId": "9053", "lightingId": "4049"}, {"elementId": "10198", "name": "amaran 100x", "nomalImage": "group_amaran100x_reflector_n", "selectedImage": "group_amaran100x_reflector_p", "type": 9, "combinationType": 14, "accessoryId": "9047", "lightingId": "4049"}, {"elementId": "10199", "name": "amaran 200x", "nomalImage": "alight_amaran_200x_n", "selectedImage": "alight_amaran_200x_p", "type": 9, "combinationType": 14, "accessoryId": "9058", "lightingId": "4051"}, {"elementId": "10200", "name": "amaran 200x", "nomalImage": "group_amaran200x_barndoor_n", "selectedImage": "group_amaran200x_barndoor_p", "type": 9, "combinationType": 14, "accessoryId": "9052", "lightingId": "4051"}, {"elementId": "10201", "name": "amaran 200x", "nomalImage": "group_amaran200x_dome_n", "selectedImage": "group_amaran200x_dome_p", "type": 9, "combinationType": 14, "accessoryId": "9049", "lightingId": "4051"}, {"elementId": "10202", "name": "amaran 200x", "nomalImage": "group_amaran200x_dome120_n", "selectedImage": "group_amaran200x_dome120_p", "type": 9, "combinationType": 14, "accessoryId": "9054", "lightingId": "4051"}, {"elementId": "10203", "name": "amaran 200x", "nomalImage": "group_amaran200x_domemini_n", "selectedImage": "group_amaran200x_domemini_p", "type": 9, "combinationType": 14, "accessoryId": "9048", "lightingId": "4051"}, {"elementId": "10204", "name": "amaran 200x", "nomalImage": "group_amaran200x_fresnel_n", "selectedImage": "group_amaran200x_fresnel_p", "type": 9, "combinationType": 14, "accessoryId": "9050", "lightingId": "4051"}, {"elementId": "10205", "name": "amaran 200x", "nomalImage": "group_amaran200x_fresnel2x_n", "selectedImage": "group_amaran200x_fresnel2x_p", "type": 9, "combinationType": 14, "accessoryId": "9051", "lightingId": "4051"}, {"elementId": "10206", "name": "amaran 200x", "nomalImage": "group_amaran200x_spotlight_n", "selectedImage": "group_amaran200x_spotlight_p", "type": 9, "combinationType": 14, "accessoryId": "9053", "lightingId": "4051"}, {"elementId": "10207", "name": "amaran 200x", "nomalImage": "group_amaran200x_reflector_n", "selectedImage": "group_amaran200x_reflector_p", "type": 9, "combinationType": 14, "accessoryId": "9047", "lightingId": "4051"}, {"elementId": "10208", "name": "amaran 200d", "nomalImage": "alight_amaran_200d_n", "selectedImage": "alight_amaran_200d_p", "type": 9, "combinationType": 14, "accessoryId": "9058", "lightingId": "4052"}, {"elementId": "10209", "name": "amaran 200d", "nomalImage": "group_amaran200d_barndoor_n", "selectedImage": "group_amaran200d_barndoor_p", "type": 9, "combinationType": 14, "accessoryId": "9052", "lightingId": "4052"}, {"elementId": "10210", "name": "amaran 200d", "nomalImage": "group_amaran200d_dome_n", "selectedImage": "group_amaran200d_dome_p", "type": 9, "combinationType": 14, "accessoryId": "9049", "lightingId": "4052"}, {"elementId": "10211", "name": "amaran 200d", "nomalImage": "group_amaran200d_dome120_n", "selectedImage": "group_amaran200d_dome120_p", "type": 9, "combinationType": 14, "accessoryId": "9054", "lightingId": "4052"}, {"elementId": "10212", "name": "amaran 200d", "nomalImage": "group_amaran200d_domemini_n", "selectedImage": "group_amaran200d_domemini_p", "type": 9, "combinationType": 14, "accessoryId": "9048", "lightingId": "4052"}, {"elementId": "10213", "name": "amaran 200d", "nomalImage": "group_amaran200d_fresnel_n", "selectedImage": "group_amaran200d_fresnel_p", "type": 9, "combinationType": 14, "accessoryId": "9050", "lightingId": "4052"}, {"elementId": "10214", "name": "amaran 200d", "nomalImage": "group_amaran200d_fresnel2x_n", "selectedImage": "group_amaran200d_fresnel2x_p", "type": 9, "combinationType": 14, "accessoryId": "9051", "lightingId": "4052"}, {"elementId": "10215", "name": "amaran 200d", "nomalImage": "group_amaran200d_spotlight_n", "selectedImage": "group_amaran200d_spotlight_p", "type": 9, "combinationType": 14, "accessoryId": "9053", "lightingId": "4052"}, {"elementId": "10216", "name": "amaran 200d", "nomalImage": "group_amaran200d_reflector_n", "selectedImage": "group_amaran200d_reflector_p", "type": 9, "combinationType": 14, "accessoryId": "9047", "lightingId": "4052"}, {"elementId": "10217", "name": "amaran 100x", "nomalImage": "group_amaran100x_lightbox6090_n", "selectedImage": "group_amaran100x_lightbox6090_p", "type": 9, "combinationType": 14, "accessoryId": "9055", "lightingId": "4049"}, {"elementId": "10218", "name": "amaran 100x", "nomalImage": "group_amaran100x_lightbox6090grid_n", "selectedImage": "group_amaran100x_lightbox6090grid_p", "type": 9, "combinationType": 14, "accessoryId": "9056", "lightingId": "4049"}, {"elementId": "10219", "name": "amaran 100x", "nomalImage": "group_amaran100x_lightbox30120_n", "selectedImage": "group_amaran100x_lightbox30120_p", "type": 9, "combinationType": 14, "accessoryId": "9057", "lightingId": "4049"}, {"elementId": "10220", "name": "amaran 100d", "nomalImage": "group_amaran100d_lightbox6090_n", "selectedImage": "group_amaran100d_lightbox6090_p", "type": 9, "combinationType": 14, "accessoryId": "9055", "lightingId": "4050"}, {"elementId": "10221", "name": "amaran 100d", "nomalImage": "group_amaran100d_lightbox6090grid_n", "selectedImage": "group_amaran100d_lightbox6090grid_p", "type": 9, "combinationType": 14, "accessoryId": "9056", "lightingId": "4050"}, {"elementId": "10222", "name": "amaran 100d", "nomalImage": "group_amaran100d_lightbox30120_n", "selectedImage": "group_amaran100d_lightbox30120_p", "type": 9, "combinationType": 14, "accessoryId": "9057", "lightingId": "4050"}, {"elementId": "10223", "name": "amaran 200x", "nomalImage": "group_amaran200x_lightbox6090_n", "selectedImage": "group_amaran200x_lightbox6090_p", "type": 9, "combinationType": 14, "accessoryId": "9055", "lightingId": "4051"}, {"elementId": "10224", "name": "amaran 200x", "nomalImage": "group_amaran200x_lightbox6090grid_n", "selectedImage": "group_amaran200x_lightbox6090grid_p", "type": 9, "combinationType": 14, "accessoryId": "9056", "lightingId": "4051"}, {"elementId": "10225", "name": "amaran 200x", "nomalImage": "group_amaran200x_lightbox30120_n", "selectedImage": "group_amaran200x_lightbox30120_p", "type": 9, "combinationType": 14, "accessoryId": "9057", "lightingId": "4051"}, {"elementId": "10226", "name": "amaran 200d", "nomalImage": "group_amaran200d_lightbox6090_n", "selectedImage": "group_amaran200d_lightbox6090_p", "type": 9, "combinationType": 14, "accessoryId": "9055", "lightingId": "4052"}, {"elementId": "10227", "name": "amaran 200d", "nomalImage": "group_amaran200d_lightbox6090grid_n", "selectedImage": "group_amaran200d_lightbox6090grid_p", "type": 9, "combinationType": 14, "accessoryId": "9056", "lightingId": "4052"}, {"elementId": "10228", "name": "amaran 200d", "nomalImage": "group_amaran200d_lightbox30120_n", "selectedImage": "group_amaran200d_lightbox30120_p", "type": 9, "combinationType": 14, "accessoryId": "9057", "lightingId": "4052"}, {"elementId": "10229", "name": "LS 600d", "nomalImage": "group_ls600d_reflector_n", "selectedImage": "group_ls600d_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4266"}, {"elementId": "10230", "name": "LS 600d", "nomalImage": "group_ls600d_domemini_n", "selectedImage": "group_ls600d_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4266"}, {"elementId": "10231", "name": "LS 600d", "nomalImage": "group_ls600d_dome_n", "selectedImage": "group_ls600d_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4266"}, {"elementId": "10232", "name": "LS 600d", "nomalImage": "group_ls600d_fresnel_n", "selectedImage": "group_ls600d_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4266"}, {"elementId": "10233", "name": "LS 600d", "nomalImage": "group_ls600d_fresnel2x_n", "selectedImage": "group_ls600d_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4266"}, {"elementId": "10234", "name": "LS 600d", "nomalImage": "group_ls600d_barndoor_n", "selectedImage": "group_ls600d_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4266"}, {"elementId": "10235", "name": "LS 600d", "nomalImage": "group_ls600d_spotlight_n", "selectedImage": "group_ls600d_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4266"}, {"elementId": "10236", "name": "LS 600d", "nomalImage": "group_ls600d_dome120_n", "selectedImage": "group_ls600d_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4266"}, {"elementId": "10237", "name": "LS 600d", "nomalImage": "group_ls600d_lightbox6090_n", "selectedImage": "group_ls600d_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4266"}, {"elementId": "10238", "name": "LS 600d", "nomalImage": "group_ls600d_lightbox6090grid_n", "selectedImage": "group_ls600d_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4266"}, {"elementId": "10239", "name": "LS 600d", "nomalImage": "group_ls600d_lightbox30120_n", "selectedImage": "group_ls600d_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4266"}, {"elementId": "10240", "name": "LS 600c Pro", "nomalImage": "group_600cpro_reflector_n", "selectedImage": "group_600cpro_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4267"}, {"elementId": "10241", "name": "LS 600c Pro", "nomalImage": "group_600cpro_domemini_n", "selectedImage": "group_600cpro_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4267"}, {"elementId": "10242", "name": "LS 600c Pro", "nomalImage": "group_600cpro_dome_n", "selectedImage": "group_600cpro_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4267"}, {"elementId": "10243", "name": "LS 600c Pro", "nomalImage": "group_600cpro_fresnel_n", "selectedImage": "group_600cpro_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4267"}, {"elementId": "10244", "name": "LS 600c Pro", "nomalImage": "group_600cpro_fresnel2x_n", "selectedImage": "group_600cpro_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4267"}, {"elementId": "10245", "name": "LS 600c Pro", "nomalImage": "group_600cpro_barndoor_n", "selectedImage": "group_600cpro_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4267"}, {"elementId": "10246", "name": "LS 600c Pro", "nomalImage": "group_600cpro_spotlight_n", "selectedImage": "group_600cpro_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4267"}, {"elementId": "10247", "name": "LS 600c Pro", "nomalImage": "group_600cpro_dome120_n", "selectedImage": "group_600cpro_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4267"}, {"elementId": "10248", "name": "LS 600c Pro", "nomalImage": "group_600cpro_lightbox6090_n", "selectedImage": "group_600cpro_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4267"}, {"elementId": "10249", "name": "LS 600c Pro", "nomalImage": "group_600cpro_lightbox6090grid_n", "selectedImage": "group_600cpro_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4267"}, {"elementId": "10250", "name": "LS 600c Pro", "nomalImage": "group_600cpro_lightbox30120_n", "selectedImage": "group_600cpro_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4267"}, {"elementId": "10251", "name": "amaran PT2c", "nomalImage": "group_pt2c_grid_n", "selectedImage": "group_pt2c_grid_p", "type": 9, "combinationType": 12, "accessoryId": "9043", "lightingId": "4269"}, {"elementId": "10252", "name": "amaran PT2c", "nomalImage": "alight_pt2c_n", "selectedImage": "alight_pt2c_p", "type": 9, "combinationType": 12, "accessoryId": "9044", "lightingId": "4269"}, {"elementId": "10253", "name": "amaran PT4c", "nomalImage": "group_pt4c_grid_n", "selectedImage": "group_pt4c_grid_p", "type": 9, "combinationType": 13, "accessoryId": "9045", "lightingId": "4270"}, {"elementId": "10254", "name": "amaran PT4c", "nomalImage": "alight_pt4c_n", "selectedImage": "alight_pt4c_p", "type": 9, "combinationType": 13, "accessoryId": "9046", "lightingId": "4270"}, {"elementId": "10255", "name": "amaran 150c", "nomalImage": "group_amaran150c_reflector_n", "selectedImage": "group_amaran150c_reflector_p", "type": 9, "combinationType": 14, "accessoryId": "9047", "lightingId": "4271"}, {"elementId": "10256", "name": "amaran 150c", "nomalImage": "group_amaran150c_domemini_n", "selectedImage": "group_amaran150c_domemini_p", "type": 9, "combinationType": 14, "accessoryId": "9048", "lightingId": "4271"}, {"elementId": "10257", "name": "amaran 150c", "nomalImage": "group_amaran150c_dome_n", "selectedImage": "group_amaran150c_dome_p", "type": 9, "combinationType": 14, "accessoryId": "9049", "lightingId": "4271"}, {"elementId": "10258", "name": "amaran 150c", "nomalImage": "group_amaran150c_fresnel_n", "selectedImage": "group_amaran150c_fresnel_p", "type": 9, "combinationType": 14, "accessoryId": "9050", "lightingId": "4271"}, {"elementId": "10259", "name": "amaran 150c", "nomalImage": "group_amaran150c_fresnel2x_n", "selectedImage": "group_amaran150c_fresnel2x_p", "type": 9, "combinationType": 14, "accessoryId": "9051", "lightingId": "4271"}, {"elementId": "10260", "name": "amaran 150c", "nomalImage": "group_amaran150c_barndoor_n", "selectedImage": "group_amaran150c_barndoor_p", "type": 9, "combinationType": 14, "accessoryId": "9052", "lightingId": "4271"}, {"elementId": "10261", "name": "amaran 150c", "nomalImage": "group_amaran150c_spotlight_n", "selectedImage": "group_amaran150c_spotlight_p", "type": 9, "combinationType": 14, "accessoryId": "9053", "lightingId": "4271"}, {"elementId": "10262", "name": "amaran 150c", "nomalImage": "group_amaran150c_dome120_n", "selectedImage": "group_amaran150c_dome120_p", "type": 9, "combinationType": 14, "accessoryId": "9054", "lightingId": "4271"}, {"elementId": "10263", "name": "amaran 150c", "nomalImage": "group_amaran150c_lightbox6090_n", "selectedImage": "group_amaran150c_lightbox6090_p", "type": 9, "combinationType": 14, "accessoryId": "9055", "lightingId": "4271"}, {"elementId": "10264", "name": "amaran 150c", "nomalImage": "group_amaran150c_lightbox6090grid_n", "selectedImage": "group_amaran150c_lightbox6090grid_p", "type": 9, "combinationType": 14, "accessoryId": "9056", "lightingId": "4271"}, {"elementId": "10265", "name": "amaran 150c", "nomalImage": "group_amaran150c_lightbox30120_n", "selectedImage": "group_amaran150c_lightbox30120_p", "type": 9, "combinationType": 14, "accessoryId": "9057", "lightingId": "4271"}, {"elementId": "10266", "name": "amaran 150c", "nomalImage": "alight_amaran_150c_n", "selectedImage": "alight_amaran_150c_p", "type": 9, "combinationType": 14, "accessoryId": "9058", "lightingId": "4271"}, {"elementId": "10267", "name": "amaran 300c", "nomalImage": "group_amaran300c_reflector_n", "selectedImage": "group_amaran300c_reflector_p", "type": 9, "combinationType": 14, "accessoryId": "9047", "lightingId": "4272"}, {"elementId": "10268", "name": "amaran 300c", "nomalImage": "group_amaran300c_domemini_n", "selectedImage": "group_amaran300c_domemini_p", "type": 9, "combinationType": 14, "accessoryId": "9048", "lightingId": "4272"}, {"elementId": "10269", "name": "amaran 300c", "nomalImage": "group_amaran300c_dome_n", "selectedImage": "group_amaran300c_dome_p", "type": 9, "combinationType": 14, "accessoryId": "9049", "lightingId": "4272"}, {"elementId": "10270", "name": "amaran 300c", "nomalImage": "group_amaran300c_fresnel_n", "selectedImage": "group_amaran300c_fresnel_p", "type": 9, "combinationType": 14, "accessoryId": "9050", "lightingId": "4272"}, {"elementId": "10271", "name": "amaran 300c", "nomalImage": "group_amaran300c_fresnel2x_n", "selectedImage": "group_amaran300c_fresnel2x_p", "type": 9, "combinationType": 14, "accessoryId": "9051", "lightingId": "4272"}, {"elementId": "10272", "name": "amaran 300c", "nomalImage": "group_amaran300c_barndoor_n", "selectedImage": "group_amaran300c_barndoor_p", "type": 9, "combinationType": 14, "accessoryId": "9052", "lightingId": "4272"}, {"elementId": "10273", "name": "amaran 300c", "nomalImage": "group_amaran300c_spotlight_n", "selectedImage": "group_amaran300c_spotlight_p", "type": 9, "combinationType": 14, "accessoryId": "9053", "lightingId": "4272"}, {"elementId": "10274", "name": "amaran 300c", "nomalImage": "group_amaran300c_dome120_n", "selectedImage": "group_amaran300c_dome120_p", "type": 9, "combinationType": 14, "accessoryId": "9054", "lightingId": "4272"}, {"elementId": "10275", "name": "amaran 300c", "nomalImage": "group_amaran300c_lightbox6090_n", "selectedImage": "group_amaran300c_lightbox6090_p", "type": 9, "combinationType": 14, "accessoryId": "9055", "lightingId": "4272"}, {"elementId": "10276", "name": "amaran 300c", "nomalImage": "group_amaran300c_lightbox6090grid_n", "selectedImage": "group_amaran300c_lightbox6090grid_p", "type": 9, "combinationType": 14, "accessoryId": "9056", "lightingId": "4272"}, {"elementId": "10277", "name": "amaran 300c", "nomalImage": "group_amaran300c_lightbox30120_n", "selectedImage": "group_amaran300c_lightbox30120_p", "type": 9, "combinationType": 14, "accessoryId": "9057", "lightingId": "4272"}, {"elementId": "10278", "name": "amaran 300c", "nomalImage": "alight_amaran_300c_n", "selectedImage": "alight_amaran_300c_p", "type": 9, "combinationType": 14, "accessoryId": "9058", "lightingId": "4272"}, {"elementId": "10279", "name": "MT Pro", "nomalImage": "group_mtpro_grid_n", "selectedImage": "group_mtpro_grid_p", "type": 9, "combinationType": 15, "accessoryId": "9059", "lightingId": "4273"}, {"elementId": "10280", "name": "MT Pro", "nomalImage": "scene_mtpro_n", "selectedImage": "scene_mtpro_p", "type": 9, "combinationType": 15, "accessoryId": "9060", "lightingId": "4273"}, {"elementId": "10281", "name": "INFINIBAR PB3", "nomalImage": "group_pb3_grid_n", "selectedImage": "group_pb3_grid_p", "type": 9, "combinationType": 16, "accessoryId": "9061", "lightingId": "4275"}, {"elementId": "10282", "name": "INFINIBAR PB3", "nomalImage": "group_pb3_softbox_n", "selectedImage": "group_pb3_softbox_p", "type": 9, "combinationType": 16, "accessoryId": "9062", "lightingId": "4275"}, {"elementId": "10283", "name": "INFINIBAR PB3", "nomalImage": "scene_pb3_n", "selectedImage": "scene_pb3_p", "type": 9, "combinationType": 16, "accessoryId": "9063", "lightingId": "4275"}, {"elementId": "10284", "name": "INFINIBAR PB6", "nomalImage": "group_pb6_grid_n", "selectedImage": "group_pb6_grid_p", "type": 9, "combinationType": 17, "accessoryId": "9064", "lightingId": "4276"}, {"elementId": "10285", "name": "INFINIBAR PB6", "nomalImage": "group_pb6_softbox_n", "selectedImage": "group_pb6_softbox_p", "type": 9, "combinationType": 17, "accessoryId": "9065", "lightingId": "4276"}, {"elementId": "10286", "name": "INFINIBAR PB6", "nomalImage": "scene_pb6_n", "selectedImage": "scene_pb6_p", "type": 9, "combinationType": 17, "accessoryId": "9066", "lightingId": "4276"}, {"elementId": "10287", "name": "INFINIBAR PB12", "nomalImage": "group_pb12_grid_n", "selectedImage": "group_pb12_grid_p", "type": 9, "combinationType": 18, "accessoryId": "9067", "lightingId": "4277"}, {"elementId": "10288", "name": "INFINIBAR PB12", "nomalImage": "group_pb12_softbox_n", "selectedImage": "group_pb12_softbox_p", "type": 9, "combinationType": 18, "accessoryId": "9068", "lightingId": "4277"}, {"elementId": "10289", "name": "INFINIBAR PB12", "nomalImage": "scene_pb12_n", "selectedImage": "scene_pb12_p", "type": 9, "combinationType": 18, "accessoryId": "9069", "lightingId": "4277"}, {"elementId": "10290", "name": "amaran 100d S", "nomalImage": "alight_amaran_100ds_n", "selectedImage": "alight_amaran_100ds_p", "type": 9, "combinationType": 19, "accessoryId": "9081", "lightingId": "4278"}, {"elementId": "10291", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_reflector_n", "selectedImage": "group_amaran100ds_reflector_p", "type": 9, "combinationType": 19, "accessoryId": "9070", "lightingId": "4278"}, {"elementId": "10292", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_fresnel2x_n", "selectedImage": "group_amaran100ds_fresnel2x_p", "type": 9, "combinationType": 19, "accessoryId": "9074", "lightingId": "4278"}, {"elementId": "10293", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_domese_n", "selectedImage": "group_amaran100ds_domese_p", "type": 9, "combinationType": 19, "accessoryId": "9072", "lightingId": "4278"}, {"elementId": "10294", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_domemini_n", "selectedImage": "group_amaran100ds_domemini_p", "type": 9, "combinationType": 19, "accessoryId": "9071", "lightingId": "4278"}, {"elementId": "10295", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_dome120_n", "selectedImage": "group_amaran100ds_dome120_p", "type": 9, "combinationType": 19, "accessoryId": "9076", "lightingId": "4278"}, {"elementId": "10296", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_lightbox30120_n", "selectedImage": "group_amaran100ds_lightbox30120_p", "type": 9, "combinationType": 19, "accessoryId": "9078", "lightingId": "4278"}, {"elementId": "10297", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_lightbox6090_n", "selectedImage": "group_amaran100ds_lightbox6090_p", "type": 9, "combinationType": 19, "accessoryId": "9077", "lightingId": "4278"}, {"elementId": "10298", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_dome2_n", "selectedImage": "group_amaran100ds_dome2_p", "type": 9, "combinationType": 19, "accessoryId": "9073", "lightingId": "4278"}, {"elementId": "10299", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_lantern90_n", "selectedImage": "group_amaran100ds_lantern90_p", "type": 9, "combinationType": 19, "accessoryId": "9079", "lightingId": "4278"}, {"elementId": "10300", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_lanternmini_n", "selectedImage": "group_amaran100ds_lanternmini_p", "type": 9, "combinationType": 19, "accessoryId": "9080", "lightingId": "4278"}, {"elementId": "10301", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_spotlight_n", "selectedImage": "group_amaran100ds_spotlight_p", "type": 9, "combinationType": 19, "accessoryId": "9075", "lightingId": "4278"}, {"elementId": "10302", "name": "amaran 100x S", "nomalImage": "alight_amaran_200ds_n", "selectedImage": "alight_amaran_200ds_p", "type": 9, "combinationType": 19, "accessoryId": "9081", "lightingId": "4279"}, {"elementId": "10303", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_reflector_n", "selectedImage": "group_amaran100xs_reflector_p", "type": 9, "combinationType": 19, "accessoryId": "9070", "lightingId": "4279"}, {"elementId": "10304", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_fresnel2x_n", "selectedImage": "group_amaran100xs_fresnel2x_p", "type": 9, "combinationType": 19, "accessoryId": "9074", "lightingId": "4279"}, {"elementId": "10305", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_domese_n", "selectedImage": "group_amaran100xs_domese_p", "type": 9, "combinationType": 19, "accessoryId": "9072", "lightingId": "4279"}, {"elementId": "10306", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_domemini_n", "selectedImage": "group_amaran100xs_domemini_p", "type": 9, "combinationType": 19, "accessoryId": "9071", "lightingId": "4279"}, {"elementId": "10307", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_dome120_n", "selectedImage": "group_amaran100xs_dome120_p", "type": 9, "combinationType": 19, "accessoryId": "9076", "lightingId": "4279"}, {"elementId": "10308", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_lightbox30120_n", "selectedImage": "group_amaran100xs_lightbox30120_p", "type": 9, "combinationType": 19, "accessoryId": "9078", "lightingId": "4279"}, {"elementId": "10309", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_lightbox6090_n", "selectedImage": "group_amaran100xs_lightbox6090_p", "type": 9, "combinationType": 19, "accessoryId": "9077", "lightingId": "4279"}, {"elementId": "10310", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_dome2_n", "selectedImage": "group_amaran100xs_dome2_p", "type": 9, "combinationType": 19, "accessoryId": "9073", "lightingId": "4279"}, {"elementId": "10311", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_lantern90_n", "selectedImage": "group_amaran100xs_lantern90_p", "type": 9, "combinationType": 19, "accessoryId": "9079", "lightingId": "4279"}, {"elementId": "10312", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_lanternmini_n", "selectedImage": "group_amaran100xs_lanternmini_p", "type": 9, "combinationType": 19, "accessoryId": "9080", "lightingId": "4279"}, {"elementId": "10313", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_spotlight_n", "selectedImage": "group_amaran100xs_spotlight_p", "type": 9, "combinationType": 19, "accessoryId": "9075", "lightingId": "4279"}, {"elementId": "10314", "name": "amaran 200d S", "nomalImage": "alight_amaran_200ds_n", "selectedImage": "alight_amaran_200ds_p", "type": 9, "combinationType": 19, "accessoryId": "9081", "lightingId": "4280"}, {"elementId": "10315", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_reflector_n", "selectedImage": "group_amaran200ds_reflector_p", "type": 9, "combinationType": 19, "accessoryId": "9070", "lightingId": "4280"}, {"elementId": "10316", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_fresnel2x_n", "selectedImage": "group_amaran200ds_fresnel2x_p", "type": 9, "combinationType": 19, "accessoryId": "9074", "lightingId": "4280"}, {"elementId": "10317", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_domese_n", "selectedImage": "group_amaran200ds_domese_p", "type": 9, "combinationType": 19, "accessoryId": "9072", "lightingId": "4280"}, {"elementId": "10318", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_domemini_n", "selectedImage": "group_amaran200ds_domemini_p", "type": 9, "combinationType": 19, "accessoryId": "9071", "lightingId": "4280"}, {"elementId": "10319", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_dome120_n", "selectedImage": "group_amaran200ds_dome120_p", "type": 9, "combinationType": 19, "accessoryId": "9076", "lightingId": "4280"}, {"elementId": "10320", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_lightbox30120_n", "selectedImage": "group_amaran200ds_lightbox30120_p", "type": 9, "combinationType": 19, "accessoryId": "9078", "lightingId": "4280"}, {"elementId": "10321", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_lightbox6090_n", "selectedImage": "group_amaran200ds_lightbox6090_p", "type": 9, "combinationType": 19, "accessoryId": "9077", "lightingId": "4280"}, {"elementId": "10322", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_dome2_n", "selectedImage": "group_amaran200ds_dome2_p", "type": 9, "combinationType": 19, "accessoryId": "9073", "lightingId": "4280"}, {"elementId": "10323", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_lantern90_n", "selectedImage": "group_amaran200ds_lantern90_p", "type": 9, "combinationType": 19, "accessoryId": "9079", "lightingId": "4280"}, {"elementId": "10324", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_lanternmini_n", "selectedImage": "group_amaran200ds_lanternmini_p", "type": 9, "combinationType": 19, "accessoryId": "9080", "lightingId": "4280"}, {"elementId": "10325", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_spotlight_n", "selectedImage": "group_amaran200ds_spotlight_p", "type": 9, "combinationType": 19, "accessoryId": "9075", "lightingId": "4280"}, {"elementId": "10326", "name": "amaran 200x S", "nomalImage": "alight_amaran_200xs_n", "selectedImage": "alight_amaran_200xs_p", "type": 9, "combinationType": 19, "accessoryId": "9081", "lightingId": "4281"}, {"elementId": "10327", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_reflector_n", "selectedImage": "group_amaran200xs_reflector_p", "type": 9, "combinationType": 19, "accessoryId": "9070", "lightingId": "4281"}, {"elementId": "10328", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_fresnel2x_n", "selectedImage": "group_amaran200xs_fresnel2x_p", "type": 9, "combinationType": 19, "accessoryId": "9074", "lightingId": "4281"}, {"elementId": "10329", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_domese_n", "selectedImage": "group_amaran200xs_domese_p", "type": 9, "combinationType": 19, "accessoryId": "9072", "lightingId": "4281"}, {"elementId": "10330", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_domemini_n", "selectedImage": "group_amaran200xs_domemini_p", "type": 9, "combinationType": 19, "accessoryId": "9071", "lightingId": "4281"}, {"elementId": "10331", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_dome120_n", "selectedImage": "group_amaran200xs_dome120_p", "type": 9, "combinationType": 19, "accessoryId": "9076", "lightingId": "4281"}, {"elementId": "10332", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_lightbox30120_n", "selectedImage": "group_amaran200xs_lightbox30120_p", "type": 9, "combinationType": 19, "accessoryId": "9078", "lightingId": "4281"}, {"elementId": "10333", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_lightbox6090_n", "selectedImage": "group_amaran200xs_lightbox6090_p", "type": 9, "combinationType": 19, "accessoryId": "9077", "lightingId": "4281"}, {"elementId": "10334", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_dome2_n", "selectedImage": "group_amaran200xs_dome2_p", "type": 9, "combinationType": 19, "accessoryId": "9073", "lightingId": "4281"}, {"elementId": "10335", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_lantern90_n", "selectedImage": "group_amaran200xs_lantern90_p", "type": 9, "combinationType": 19, "accessoryId": "9079", "lightingId": "4281"}, {"elementId": "10336", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_lanternmini_n", "selectedImage": "group_amaran200xs_lanternmini_p", "type": 9, "combinationType": 19, "accessoryId": "9080", "lightingId": "4281"}, {"elementId": "10337", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_spotlight_n", "selectedImage": "group_amaran200xs_spotlight_p", "type": 9, "combinationType": 19, "accessoryId": "9075", "lightingId": "4281"}, {"elementId": "10338", "name": "amaran 60d S", "nomalImage": "group_cob60d_reflector_n", "selectedImage": "group_cob60d_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4282"}, {"elementId": "10339", "name": "amaran 60d S", "nomalImage": "group_cob60d_domemini_n", "selectedImage": "group_cob60d_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4282"}, {"elementId": "10340", "name": "amaran 60d S", "nomalImage": "group_cob60d_dome_n", "selectedImage": "group_cob60d_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4282"}, {"elementId": "10341", "name": "amaran 60d S", "nomalImage": "group_cob60d_fresnel_n", "selectedImage": "group_cob60d_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4282"}, {"elementId": "10342", "name": "amaran 60d S", "nomalImage": "group_cob60d_fresnel2x_n", "selectedImage": "group_cob60d_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4282"}, {"elementId": "10343", "name": "amaran 60d S", "nomalImage": "group_cob60d_barndoor_n", "selectedImage": "group_cob60d_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4282"}, {"elementId": "10344", "name": "amaran 60d S", "nomalImage": "group_cob60d_spotlight_n", "selectedImage": "group_cob60d_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4282"}, {"elementId": "10345", "name": "amaran 60d S", "nomalImage": "group_cob60d_dome120_n", "selectedImage": "group_cob60d_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4282"}, {"elementId": "10346", "name": "amaran 60d S", "nomalImage": "group_cob60d_lightbox6090_n", "selectedImage": "group_cob60d_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4282"}, {"elementId": "10347", "name": "amaran 60d S", "nomalImage": "group_cob60d_lightbox6090grid_n", "selectedImage": "group_cob60d_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4282"}, {"elementId": "10348", "name": "amaran 60d S", "nomalImage": "group_cob60d_lightbox30120_n", "selectedImage": "group_cob60d_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4282"}, {"elementId": "10349", "name": "amaran 60x S", "nomalImage": "group_cob60x_reflector_n", "selectedImage": "group_cob60x_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4283"}, {"elementId": "10350", "name": "amaran 60x S", "nomalImage": "group_cob60x_domemini_n", "selectedImage": "group_cob60x_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4283"}, {"elementId": "10351", "name": "amaran 60x S", "nomalImage": "group_cob60x_dome_n", "selectedImage": "group_cob60x_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4283"}, {"elementId": "10352", "name": "amaran 60x S", "nomalImage": "group_cob60x_fresnel_n", "selectedImage": "group_cob60x_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4283"}, {"elementId": "10353", "name": "amaran 60x S", "nomalImage": "group_cob60x_fresnel2x_n", "selectedImage": "group_cob60x_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4283"}, {"elementId": "10354", "name": "amaran 60x S", "nomalImage": "group_cob60x_barndoor_n", "selectedImage": "group_cob60x_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4283"}, {"elementId": "10355", "name": "amaran 60x S", "nomalImage": "group_cob60x_spotlight_n", "selectedImage": "group_cob60x_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4283"}, {"elementId": "10356", "name": "amaran 60x S", "nomalImage": "group_cob60x_dome120_n", "selectedImage": "group_cob60x_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4283"}, {"elementId": "10357", "name": "amaran 60x S", "nomalImage": "group_cob60x_lightbox6090_n", "selectedImage": "group_cob60x_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4283"}, {"elementId": "10358", "name": "amaran 60x S", "nomalImage": "group_cob60x_lightbox6090grid_n", "selectedImage": "group_cob60x_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4283"}, {"elementId": "10359", "name": "amaran 60x S", "nomalImage": "group_cob60x_lightbox30120_n", "selectedImage": "group_cob60x_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4283"}, {"elementId": "10361", "name": "Electro Storm CS15", "nomalImage": "alight_cs15_n", "selectedImage": "alight_cs15_p", "type": 9, "combinationType": 19, "accessoryId": "9081", "lightingId": "4284"}, {"elementId": "10362", "name": "Electro Storm CS15", "nomalImage": "group_cs15_reflector_n", "selectedImage": "group_cs15_reflector_p", "type": 9, "combinationType": 19, "accessoryId": "9070", "lightingId": "4284"}, {"elementId": "10363", "name": "Electro Storm CS15", "nomalImage": "group_cs15_fresnel2x_n", "selectedImage": "group_cs15_fresnel2x_p", "type": 9, "combinationType": 19, "accessoryId": "9074", "lightingId": "4284"}, {"elementId": "10364", "name": "Electro Storm CS15", "nomalImage": "group_cs15_domese_n", "selectedImage": "group_cs15_domese_p", "type": 9, "combinationType": 19, "accessoryId": "9072", "lightingId": "4284"}, {"elementId": "10365", "name": "Electro Storm CS15", "nomalImage": "group_cs15_domemini_n", "selectedImage": "group_cs15_domemini_p", "type": 9, "combinationType": 19, "accessoryId": "9071", "lightingId": "4284"}, {"elementId": "10366", "name": "Electro Storm CS15", "nomalImage": "group_cs15_dome120_n", "selectedImage": "group_cs15_dome120_p", "type": 9, "combinationType": 19, "accessoryId": "9076", "lightingId": "4284"}, {"elementId": "10367", "name": "Electro Storm CS15", "nomalImage": "group_cs15_lightbox30120_n", "selectedImage": "group_cs15_lightbox30120_p", "type": 9, "combinationType": 19, "accessoryId": "9078", "lightingId": "4284"}, {"elementId": "10368", "name": "Electro Storm CS15", "nomalImage": "group_cs15_lightbox6090_n", "selectedImage": "group_cs15_lightbox6090_p", "type": 9, "combinationType": 19, "accessoryId": "9077", "lightingId": "4284"}, {"elementId": "10369", "name": "Electro Storm CS15", "nomalImage": "group_cs15_dome2_n", "selectedImage": "group_cs15_dome2_p", "type": 9, "combinationType": 19, "accessoryId": "9073", "lightingId": "4284"}, {"elementId": "10370", "name": "Electro Storm CS15", "nomalImage": "group_cs15_lantern90_n", "selectedImage": "group_cs15_lantern90_p", "type": 9, "combinationType": 19, "accessoryId": "9079", "lightingId": "4284"}, {"elementId": "10371", "name": "Electro Storm CS15", "nomalImage": "group_cs15_lanternmini_n", "selectedImage": "group_cs15_lanternmini_p", "type": 9, "combinationType": 19, "accessoryId": "9080", "lightingId": "4284"}, {"elementId": "10372", "name": "Electro Storm CS15", "nomalImage": "group_cs15_spotlight_n", "selectedImage": "group_cs15_spotlight_p", "type": 9, "combinationType": 19, "accessoryId": "9075", "lightingId": "4284"}, {"elementId": "10373", "name": "Electro Storm XT26", "nomalImage": "alight_xt26_n", "selectedImage": "alight_xt26_p", "type": 9, "combinationType": 19, "accessoryId": "9081", "lightingId": "4285"}, {"elementId": "10374", "name": "Electro Storm XT26", "nomalImage": "group_xt26_reflector_n", "selectedImage": "group_xt26_reflector_p", "type": 9, "combinationType": 19, "accessoryId": "9070", "lightingId": "4285"}, {"elementId": "10375", "name": "Electro Storm XT26", "nomalImage": "group_xt26_fresnel2x_n", "selectedImage": "group_xt26_fresnel2x_p", "type": 9, "combinationType": 19, "accessoryId": "9074", "lightingId": "4285"}, {"elementId": "10376", "name": "Electro Storm XT26", "nomalImage": "group_xt26_domese_n", "selectedImage": "group_xt26_domese_p", "type": 9, "combinationType": 19, "accessoryId": "9072", "lightingId": "4285"}, {"elementId": "10377", "name": "Electro Storm XT26", "nomalImage": "group_xt26_domemini_n", "selectedImage": "group_xt26_domemini_p", "type": 9, "combinationType": 19, "accessoryId": "9071", "lightingId": "4285"}, {"elementId": "10378", "name": "Electro Storm XT26", "nomalImage": "group_xt26_dome120_n", "selectedImage": "group_xt26_dome120_p", "type": 9, "combinationType": 19, "accessoryId": "9076", "lightingId": "4285"}, {"elementId": "10379", "name": "Electro Storm XT26", "nomalImage": "group_xt26_lightbox30120_n", "selectedImage": "group_xt26_lightbox30120_p", "type": 9, "combinationType": 19, "accessoryId": "9078", "lightingId": "4285"}, {"elementId": "10380", "name": "Electro Storm XT26", "nomalImage": "group_xt26_lightbox6090_n", "selectedImage": "group_xt26_lightbox6090_p", "type": 9, "combinationType": 19, "accessoryId": "9077", "lightingId": "4285"}, {"elementId": "10381", "name": "Electro Storm XT26", "nomalImage": "group_xt26_dome2_n", "selectedImage": "group_xt26_dome2_p", "type": 9, "combinationType": 19, "accessoryId": "9073", "lightingId": "4285"}, {"elementId": "10382", "name": "Electro Storm XT26", "nomalImage": "group_xt26_lantern90_n", "selectedImage": "group_xt26_lantern90_p", "type": 9, "combinationType": 19, "accessoryId": "9079", "lightingId": "4285"}, {"elementId": "10383", "name": "Electro Storm XT26", "nomalImage": "group_xt26_lanternmini_n", "selectedImage": "group_xt26_lanternmini_p", "type": 9, "combinationType": 19, "accessoryId": "9080", "lightingId": "4285"}, {"elementId": "10384", "name": "Electro Storm XT26", "nomalImage": "group_xt26_spotlight_n", "selectedImage": "group_xt26_spotlight_p", "type": 9, "combinationType": 19, "accessoryId": "9075", "lightingId": "4285"}, {"elementId": "10385", "name": "Nova P600c", "nomalImage": "group_novap600c_barndoor_n", "selectedImage": "group_novap600c_barndoor_p", "type": 9, "combinationType": 8, "accessoryId": "9082", "lightingId": "4257"}, {"elementId": "10386", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_lantern65_n", "selectedImage": "group_amaran100xs_lantern65_p", "type": 9, "combinationType": 19, "accessoryId": "9089", "lightingId": "4279"}, {"elementId": "10387", "name": "amaran 100x S", "nomalImage": "group_amaran100xs_spotlightse_n", "selectedImage": "group_amaran100xs_spotlightse_p", "type": 9, "combinationType": 19, "accessoryId": "9090", "lightingId": "4279"}, {"elementId": "10388", "name": "amaran 100x", "nomalImage": "group_amaran100x_lantern_n", "selectedImage": "group_amaran100x_lantern_p", "type": 9, "combinationType": 14, "accessoryId": "9086", "lightingId": "4049"}, {"elementId": "10389", "name": "amaran 100x", "nomalImage": "group_amaran100x_lantern65_n", "selectedImage": "group_amaran100x_lantern65_p", "type": 9, "combinationType": 14, "accessoryId": "9087", "lightingId": "4049"}, {"elementId": "10390", "name": "amaran 100x", "nomalImage": "group_amaran100x_spotlightse_n", "selectedImage": "group_amaran100x_spotlightse_p", "type": 9, "combinationType": 14, "accessoryId": "9088", "lightingId": "4049"}, {"elementId": "10391", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_lantern65_n", "selectedImage": "group_amaran100ds_lantern65_p", "type": 9, "combinationType": 19, "accessoryId": "9089", "lightingId": "4278"}, {"elementId": "10392", "name": "amaran 100d S", "nomalImage": "group_amaran100ds_spotlightse_n", "selectedImage": "group_amaran100ds_spotlightse_p", "type": 9, "combinationType": 19, "accessoryId": "9090", "lightingId": "4278"}, {"elementId": "10393", "name": "amaran 100d", "nomalImage": "group_amaran100d_lantern_n", "selectedImage": "group_amaran100d_lantern_p", "type": 9, "combinationType": 14, "accessoryId": "9086", "lightingId": "4050"}, {"elementId": "10394", "name": "amaran 100d", "nomalImage": "group_amaran100d_lantern65_n", "selectedImage": "group_amaran100d_lantern65_p", "type": 9, "combinationType": 14, "accessoryId": "9087", "lightingId": "4050"}, {"elementId": "10395", "name": "amaran 100d", "nomalImage": "group_amaran100d_spotlightse_n", "selectedImage": "group_amaran100d_spotlightse_p", "type": 9, "combinationType": 14, "accessoryId": "9088", "lightingId": "4050"}, {"elementId": "10396", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_lantern65_n", "selectedImage": "group_amaran200xs_lantern65_p", "type": 9, "combinationType": 19, "accessoryId": "9089", "lightingId": "4281"}, {"elementId": "10397", "name": "amaran 200x S", "nomalImage": "group_amaran200xs_spotlightse_n", "selectedImage": "group_amaran200xs_spotlightse_p", "type": 9, "combinationType": 19, "accessoryId": "9090", "lightingId": "4281"}, {"elementId": "10398", "name": "amaran 200x", "nomalImage": "group_amaran200x_lantern_n", "selectedImage": "group_amaran200x_lantern_p", "type": 9, "combinationType": 14, "accessoryId": "9086", "lightingId": "4051"}, {"elementId": "10399", "name": "amaran 200x", "nomalImage": "group_amaran200x_lantern65_n", "selectedImage": "group_amaran200x_lantern65_p", "type": 9, "combinationType": 14, "accessoryId": "9087", "lightingId": "4051"}, {"elementId": "10400", "name": "amaran 200x", "nomalImage": "group_amaran200x_spotlightse_n", "selectedImage": "group_amaran200x_spotlightse_p", "type": 9, "combinationType": 14, "accessoryId": "9088", "lightingId": "4051"}, {"elementId": "10401", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_lantern65_n", "selectedImage": "group_amaran200ds_lantern65_p", "type": 9, "combinationType": 19, "accessoryId": "9089", "lightingId": "4280"}, {"elementId": "10402", "name": "amaran 200d S", "nomalImage": "group_amaran200ds_spotlightse_n", "selectedImage": "group_amaran200ds_spotlightse_p", "type": 9, "combinationType": 19, "accessoryId": "9090", "lightingId": "4280"}, {"elementId": "10403", "name": "amaran 200d", "nomalImage": "group_amaran200d_lantern_n", "selectedImage": "group_amaran200d_lantern_p", "type": 9, "combinationType": 14, "accessoryId": "9086", "lightingId": "4052"}, {"elementId": "10404", "name": "amaran 200d", "nomalImage": "group_amaran200d_lantern65_n", "selectedImage": "group_amaran200d_lantern65_p", "type": 9, "combinationType": 14, "accessoryId": "9087", "lightingId": "4052"}, {"elementId": "10405", "name": "amaran 200d", "nomalImage": "group_amaran200d_spotlightse_n", "selectedImage": "group_amaran200d_spotlightse_p", "type": 9, "combinationType": 14, "accessoryId": "9088", "lightingId": "4052"}, {"elementId": "10406", "name": "amaran COB 60d", "nomalImage": "group_cob60d_lantern_n", "selectedImage": "group_cob60d_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4253"}, {"elementId": "10407", "name": "amaran COB 60d", "nomalImage": "group_cob60d_lantern65_n", "selectedImage": "group_cob60d_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4253"}, {"elementId": "10408", "name": "amaran COB 60d", "nomalImage": "group_cob60d_spotlightse_n", "selectedImage": "group_cob60d_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4253"}, {"elementId": "10409", "name": "amaran COB 60x", "nomalImage": "group_cob60x_lantern_n", "selectedImage": "group_cob60x_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4254"}, {"elementId": "10410", "name": "amaran COB 60x", "nomalImage": "group_cob60x_lantern65_n", "selectedImage": "group_cob60x_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4254"}, {"elementId": "10411", "name": "amaran COB 60x", "nomalImage": "group_cob60x_spotlightse_n", "selectedImage": "group_cob60x_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4254"}, {"elementId": "10412", "name": "amaran 60d S", "nomalImage": "group_cob60d_lantern_n", "selectedImage": "group_cob60d_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4282"}, {"elementId": "10413", "name": "amaran 60d S", "nomalImage": "group_cob60d_lantern65_n", "selectedImage": "group_cob60d_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4282"}, {"elementId": "10414", "name": "amaran 60d S", "nomalImage": "group_cob60d_spotlightse_n", "selectedImage": "group_cob60d_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4282"}, {"elementId": "10415", "name": "amaran 60x S", "nomalImage": "group_cob60x_lantern_n", "selectedImage": "group_cob60x_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4283"}, {"elementId": "10416", "name": "amaran 60x S", "nomalImage": "group_cob60x_lantern65_n", "selectedImage": "group_cob60x_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4283"}, {"elementId": "10417", "name": "amaran 60x S", "nomalImage": "group_cob60x_spotlightse_n", "selectedImage": "group_cob60x_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4283"}, {"elementId": "10418", "name": "LS C120d", "nomalImage": "group_120d_lantern_n", "selectedImage": "group_120d_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4005"}, {"elementId": "10419", "name": "LS C120d", "nomalImage": "group_120d_lantern65_n", "selectedImage": "group_120d_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4005"}, {"elementId": "10420", "name": "LS C120d", "nomalImage": "group_120d_spotlightse_n", "selectedImage": "group_120d_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4005"}, {"elementId": "10421", "name": "LS C120dII", "nomalImage": "group_120dii_lantern_n", "selectedImage": "group_120dii_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4048"}, {"elementId": "10422", "name": "LS C120dII", "nomalImage": "group_120dii_lantern65_n", "selectedImage": "group_120dii_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4048"}, {"elementId": "10423", "name": "LS C120dII", "nomalImage": "group_120dii_spotlightse_n", "selectedImage": "group_120dii_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4048"}, {"elementId": "10424", "name": "LS C120t", "nomalImage": "group_120t_lantern_n", "selectedImage": "group_120t_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4006"}, {"elementId": "10425", "name": "LS C120t", "nomalImage": "group_120t_lantern65_n", "selectedImage": "group_120t_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4006"}, {"elementId": "10426", "name": "LS C120t", "nomalImage": "group_120t_spotlightse_n", "selectedImage": "group_120t_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4006"}, {"elementId": "10427", "name": "LS C300d", "nomalImage": "group_300d_lantern_n", "selectedImage": "group_300d_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4004"}, {"elementId": "10428", "name": "LS C300d", "nomalImage": "group_300d_lantern65_n", "selectedImage": "group_300d_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4004"}, {"elementId": "10429", "name": "LS C300d", "nomalImage": "group_300d_spotlightse_n", "selectedImage": "group_300d_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4004"}, {"elementId": "10430", "name": "LS C300dII", "nomalImage": "group_300d2_lantern_n", "selectedImage": "group_300d2_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4007"}, {"elementId": "10431", "name": "LS C300dII", "nomalImage": "group_300d2_lantern65_n", "selectedImage": "group_300d2_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4007"}, {"elementId": "10432", "name": "LS C300dII", "nomalImage": "group_300d2_spotlightse_n", "selectedImage": "group_300d2_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4007"}, {"elementId": "10433", "name": "LS 300x", "nomalImage": "group_300x_lantern_n", "selectedImage": "group_300x_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4047"}, {"elementId": "10434", "name": "LS 300x", "nomalImage": "group_300x_lantern65_n", "selectedImage": "group_300x_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4047"}, {"elementId": "10435", "name": "LS 300x", "nomalImage": "group_300x_spotlightse_n", "selectedImage": "group_300x_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4047"}, {"elementId": "10436", "name": "LS 600d", "nomalImage": "group_ls600d_lantern_n", "selectedImage": "group_ls600d_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4266"}, {"elementId": "10437", "name": "LS 600d", "nomalImage": "group_ls600d_lantern65_n", "selectedImage": "group_ls600d_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4266"}, {"elementId": "10438", "name": "LS 600d", "nomalImage": "group_ls600d_spotlightse_n", "selectedImage": "group_ls600d_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4266"}, {"elementId": "10439", "name": "LS 600d Pro", "nomalImage": "group_600d_lantern_n", "selectedImage": "group_600d_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4046"}, {"elementId": "10440", "name": "LS 600d Pro", "nomalImage": "group_600d_lantern65_n", "selectedImage": "group_600d_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4046"}, {"elementId": "10441", "name": "LS 600d Pro", "nomalImage": "group_600d_spotlightse_n", "selectedImage": "group_600d_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4046"}, {"elementId": "10442", "name": "LS 600x Pro", "nomalImage": "group_600x_lantern_n", "selectedImage": "group_600x_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4258"}, {"elementId": "10443", "name": "LS 600x Pro", "nomalImage": "group_600x_lantern65_n", "selectedImage": "group_600x_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4258"}, {"elementId": "10444", "name": "LS 600x Pro", "nomalImage": "group_600x_spotlightse_n", "selectedImage": "group_600x_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4258"}, {"elementId": "10445", "name": "LS 600c Pro", "nomalImage": "group_600cpro_lantern_n", "selectedImage": "group_600cpro_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4267"}, {"elementId": "10446", "name": "LS 600c Pro", "nomalImage": "group_600cpro_lantern65_n", "selectedImage": "group_600cpro_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4267"}, {"elementId": "10447", "name": "LS 600c Pro", "nomalImage": "group_600cpro_spotlightse_n", "selectedImage": "group_600cpro_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4267"}, {"elementId": "10448", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_lantern_n", "selectedImage": "group_ls1200dpro_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4259"}, {"elementId": "10449", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_lantern65_n", "selectedImage": "group_ls1200dpro_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4259"}, {"elementId": "10450", "name": "LS 1200d Pro", "nomalImage": "group_ls1200dpro_spotlightse_n", "selectedImage": "group_ls1200dpro_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4259"}, {"elementId": "10451", "name": "Electro Storm CS15", "nomalImage": "group_cs15_lantern65_n", "selectedImage": "group_cs15_lantern65_p", "type": 9, "combinationType": 19, "accessoryId": "9089", "lightingId": "4284"}, {"elementId": "10452", "name": "Electro Storm CS15", "nomalImage": "group_cs15_spotlightse_n", "selectedImage": "group_cs15_spotlightse_p", "type": 9, "combinationType": 19, "accessoryId": "9090", "lightingId": "4284"}, {"elementId": "10453", "name": "Electro Storm XT26", "nomalImage": "group_xt26_lantern65_n", "selectedImage": "group_xt26_lantern65_p", "type": 9, "combinationType": 19, "accessoryId": "9089", "lightingId": "4285"}, {"elementId": "10454", "name": "Electro Storm XT26", "nomalImage": "group_xt26_spotlightse_n", "selectedImage": "group_xt26_spotlightse_p", "type": 9, "combinationType": 19, "accessoryId": "9090", "lightingId": "4285"}, {"elementId": "10455", "name": "amaran 150c", "nomalImage": "group_amaran150c_lantern_n", "selectedImage": "group_amaran150c_lantern_p", "type": 9, "combinationType": 14, "accessoryId": "9086", "lightingId": "4271"}, {"elementId": "10456", "name": "amaran 150c", "nomalImage": "group_amaran150c_lantern65_n", "selectedImage": "group_amaran150c_lantern65_p", "type": 9, "combinationType": 14, "accessoryId": "9087", "lightingId": "4271"}, {"elementId": "10457", "name": "amaran 150c", "nomalImage": "group_amaran150c_spotlightse_n", "selectedImage": "group_amaran150c_spotlightse_p", "type": 9, "combinationType": 14, "accessoryId": "9088", "lightingId": "4271"}, {"elementId": "10458", "name": "amaran 300c", "nomalImage": "group_amaran300c_lantern_n", "selectedImage": "group_amaran300c_lantern_p", "type": 9, "combinationType": 14, "accessoryId": "9086", "lightingId": "4272"}, {"elementId": "10459", "name": "amaran 300c", "nomalImage": "group_amaran300c_lantern65_n", "selectedImage": "group_amaran300c_lantern65_p", "type": 9, "combinationType": 14, "accessoryId": "9087", "lightingId": "4272"}, {"elementId": "10460", "name": "amaran 300c", "nomalImage": "group_amaran300c_spotlightse_n", "selectedImage": "group_amaran300c_spotlightse_p", "type": 9, "combinationType": 14, "accessoryId": "9088", "lightingId": "4272"}, {"elementId": "10461", "name": "MC Pro", "nomalImage": "scene_almcpro_n", "selectedImage": "scene_almcpro_p", "type": 9, "combinationType": 20, "accessoryId": "9094", "lightingId": "4274"}, {"elementId": "10462", "name": "MC Pro", "nomalImage": "group_mcpro_grid_n", "selectedImage": "group_mcpro_grid_p", "type": 9, "combinationType": 20, "accessoryId": "9091", "lightingId": "4274"}, {"elementId": "10463", "name": "MC Pro", "nomalImage": "group_mcpro_dome_n", "selectedImage": "group_mcpro_dome_p", "type": 9, "combinationType": 20, "accessoryId": "9092", "lightingId": "4274"}, {"elementId": "10464", "name": "MC Pro", "nomalImage": "group_mcpro_flat_n", "selectedImage": "group_mcpro_flat_p", "type": 9, "combinationType": 20, "accessoryId": "9093", "lightingId": "4274"}, {"elementId": "10465", "name": "amaran 200c", "nomalImage": "alight_amaran_200c_n", "selectedImage": "alight_amaran_200c_p", "type": 9, "combinationType": 21, "accessoryId": "9100", "lightingId": "4286"}, {"elementId": "10466", "name": "amaran 200c", "nomalImage": "group_amaran200c_reflector_n", "selectedImage": "group_amaran200c_reflector_p", "type": 9, "combinationType": 21, "accessoryId": "9095", "lightingId": "4286"}, {"elementId": "10467", "name": "amaran 200c", "nomalImage": "group_amaran200c_barndoor_n", "selectedImage": "group_amaran200c_barndoor_p", "type": 9, "combinationType": 21, "accessoryId": "9096", "lightingId": "4286"}, {"elementId": "10468", "name": "amaran 200c", "nomalImage": "group_amaran200c_fresnel2x_n", "selectedImage": "group_amaran200c_fresnel2x_p", "type": 9, "combinationType": 21, "accessoryId": "9097", "lightingId": "4286"}, {"elementId": "10469", "name": "amaran 200c", "nomalImage": "group_amaran200c_domese_n", "selectedImage": "group_amaran200c_domese_p", "type": 9, "combinationType": 21, "accessoryId": "9098", "lightingId": "4286"}, {"elementId": "10470", "name": "amaran 200c", "nomalImage": "group_amaran200c_spotlightse_n", "selectedImage": "group_amaran200c_spotlightse_p", "type": 9, "combinationType": 21, "accessoryId": "9099", "lightingId": "4286"}, {"elementId": "10471", "name": "amaran P40x", "nomalImage": "group_amaranp40x_softbox_n", "selectedImage": "group_amaranp40x_softbox_p", "type": 9, "combinationType": 22, "accessoryId": "9101", "lightingId": "4287"}, {"elementId": "10472", "name": "amaran P40x", "nomalImage": "group_amaranp40x_softboxgrid_n", "selectedImage": "group_amaranp40x_softboxgrid_p", "type": 9, "combinationType": 22, "accessoryId": "9102", "lightingId": "4287"}, {"elementId": "10473", "name": "amaran P40x", "nomalImage": "alight_p40x_n", "selectedImage": "alight_p40x_p", "type": 9, "combinationType": 22, "accessoryId": "9103", "lightingId": "4287"}, {"elementId": "10474", "name": "STORM 1200x", "nomalImage": "group_ls1200x_reflector30_n", "selectedImage": "group_ls1200x_reflector30_p", "type": 9, "combinationType": 23, "accessoryId": "9104", "lightingId": "4288"}, {"elementId": "10475", "name": "STORM 1200x", "nomalImage": "group_ls1200x_reflector15_n", "selectedImage": "group_ls1200x_reflector15_p", "type": 9, "combinationType": 23, "accessoryId": "9105", "lightingId": "4288"}, {"elementId": "10476", "name": "STORM 1200x", "nomalImage": "group_ls1200x_reflector45_n", "selectedImage": "group_ls1200x_reflector45_p", "type": 9, "combinationType": 23, "accessoryId": "9106", "lightingId": "4288"}, {"elementId": "10477", "name": "STORM 1200x", "nomalImage": "group_ls1200x_dome2_n", "selectedImage": "group_ls1200x_dome2_p", "type": 9, "combinationType": 23, "accessoryId": "9107", "lightingId": "4288"}, {"elementId": "10478", "name": "STORM 1200x", "nomalImage": "group_ls1200x_dome3_n", "selectedImage": "group_ls1200x_dome3_p", "type": 9, "combinationType": 23, "accessoryId": "9108", "lightingId": "4288"}, {"elementId": "10479", "name": "STORM 1200x", "nomalImage": "group_ls1200x_dome120_n", "selectedImage": "group_ls1200x_dome120_p", "type": 9, "combinationType": 23, "accessoryId": "9109", "lightingId": "4288"}, {"elementId": "10480", "name": "STORM 1200x", "nomalImage": "group_ls1200x_dome150_n", "selectedImage": "group_ls1200x_dome150_p", "type": 9, "combinationType": 23, "accessoryId": "9110", "lightingId": "4288"}, {"elementId": "10481", "name": "STORM 1200x", "nomalImage": "group_ls1200x_domese_n", "selectedImage": "group_ls1200x_domese_p", "type": 9, "combinationType": 23, "accessoryId": "9111", "lightingId": "4288"}, {"elementId": "10482", "name": "STORM 1200x", "nomalImage": "group_ls1200x_cf12_n", "selectedImage": "group_ls1200x_cf12_p", "type": 9, "combinationType": 23, "accessoryId": "9112", "lightingId": "4288"}, {"elementId": "10483", "name": "STORM 1200x", "nomalImage": "group_ls1200x_spotlight_n", "selectedImage": "group_ls1200x_spotlight_p", "type": 9, "combinationType": 23, "accessoryId": "9113", "lightingId": "4288"}, {"elementId": "10484", "name": "STORM 1200x", "nomalImage": "group_ls1200x_barndoor_n", "selectedImage": "group_ls1200x_barndoor_p", "type": 9, "combinationType": 23, "accessoryId": "9114", "lightingId": "4288"}, {"elementId": "10485", "name": "STORM 1200x", "nomalImage": "group_ls1200x_lightbox6090_n", "selectedImage": "group_ls1200x_lightbox6090_p", "type": 9, "combinationType": 23, "accessoryId": "9115", "lightingId": "4288"}, {"elementId": "10486", "name": "STORM 1200x", "nomalImage": "group_ls1200x_lantern_n", "selectedImage": "group_ls1200x_lantern_p", "type": 9, "combinationType": 23, "accessoryId": "9116", "lightingId": "4288"}, {"elementId": "10487", "name": "STORM 1200x", "nomalImage": "group_ls1200x_lantern_90_n", "selectedImage": "group_ls1200x_lantern_90_p", "type": 9, "combinationType": 23, "accessoryId": "9117", "lightingId": "4288"}, {"elementId": "10488", "name": "STORM 1200x", "nomalImage": "group_ls1200x_transferbowl_n", "selectedImage": "group_ls1200x_transferbowl_p", "type": 9, "combinationType": 23, "accessoryId": "9118", "lightingId": "4288"}, {"elementId": "10489", "name": "STORM 1000c", "nomalImage": "group_ls1200x_reflector30_n", "selectedImage": "group_ls1200x_reflector30_p", "type": 9, "combinationType": 23, "accessoryId": "9104", "lightingId": "4289"}, {"elementId": "10490", "name": "STORM 1000c", "nomalImage": "group_ls1200x_reflector15_n", "selectedImage": "group_ls1200x_reflector15_p", "type": 9, "combinationType": 23, "accessoryId": "9105", "lightingId": "4289"}, {"elementId": "10491", "name": "STORM 1000c", "nomalImage": "group_ls1200x_reflector45_n", "selectedImage": "group_ls1200x_reflector45_p", "type": 9, "combinationType": 23, "accessoryId": "9106", "lightingId": "4289"}, {"elementId": "10492", "name": "STORM 1000c", "nomalImage": "group_ls1200x_dome2_n", "selectedImage": "group_ls1200x_dome2_p", "type": 9, "combinationType": 23, "accessoryId": "9107", "lightingId": "4289"}, {"elementId": "10493", "name": "STORM 1000c", "nomalImage": "group_ls1200x_dome3_n", "selectedImage": "group_ls1200x_dome3_p", "type": 9, "combinationType": 23, "accessoryId": "9108", "lightingId": "4289"}, {"elementId": "10494", "name": "STORM 1000c", "nomalImage": "group_ls1200x_dome120_n", "selectedImage": "group_ls1200x_dome120_p", "type": 9, "combinationType": 23, "accessoryId": "9109", "lightingId": "4289"}, {"elementId": "10495", "name": "STORM 1000c", "nomalImage": "group_ls1200x_dome150_n", "selectedImage": "group_ls1200x_dome150_p", "type": 9, "combinationType": 23, "accessoryId": "9110", "lightingId": "4289"}, {"elementId": "10496", "name": "STORM 1000c", "nomalImage": "group_ls1200x_domese_n", "selectedImage": "group_ls1200x_domese_p", "type": 9, "combinationType": 23, "accessoryId": "9111", "lightingId": "4289"}, {"elementId": "10497", "name": "STORM 1000c", "nomalImage": "group_ls1200x_cf12_n", "selectedImage": "group_ls1200x_cf12_p", "type": 9, "combinationType": 23, "accessoryId": "9112", "lightingId": "4289"}, {"elementId": "10498", "name": "STORM 1000c", "nomalImage": "group_ls1200x_spotlight_n", "selectedImage": "group_ls1200x_spotlight_p", "type": 9, "combinationType": 23, "accessoryId": "9113", "lightingId": "4289"}, {"elementId": "10499", "name": "STORM 1000c", "nomalImage": "group_ls1200x_barndoor_n", "selectedImage": "group_ls1200x_barndoor_p", "type": 9, "combinationType": 23, "accessoryId": "9114", "lightingId": "4289"}, {"elementId": "10500", "name": "STORM 1000c", "nomalImage": "group_ls1200x_lightbox6090_n", "selectedImage": "group_ls1200x_lightbox6090_p", "type": 9, "combinationType": 23, "accessoryId": "9115", "lightingId": "4289"}, {"elementId": "10501", "name": "STORM 1000c", "nomalImage": "group_ls1200x_lantern_n", "selectedImage": "group_ls1200x_lantern_p", "type": 9, "combinationType": 23, "accessoryId": "9116", "lightingId": "4289"}, {"elementId": "10502", "name": "STORM 1000c", "nomalImage": "group_ls1200x_lantern_90_n", "selectedImage": "group_ls1200x_lantern_90_p", "type": 9, "combinationType": 23, "accessoryId": "9117", "lightingId": "4289"}, {"elementId": "10503", "name": "STORM 1000c", "nomalImage": "group_ls1200x_transferbowl_n", "selectedImage": "group_ls1200x_transferbowl_p", "type": 9, "combinationType": 23, "accessoryId": "9118", "lightingId": "4289"}, {"elementId": "10504", "name": "STORM 80c", "nomalImage": "group_aputure80c_standardphoto_n", "selectedImage": "group_aputure80c_standardphoto_p", "type": 9, "combinationType": 24, "accessoryId": "9119", "lightingId": "4290"}, {"elementId": "10505", "name": "STORM 80c", "nomalImage": "group_aputure80c_diffuser_n", "selectedImage": "group_aputure80c_diffuser_p", "type": 9, "combinationType": 24, "accessoryId": "9120", "lightingId": "4290"}, {"elementId": "10506", "name": "STORM 80c", "nomalImage": "group_aputure80c_barndoor_n", "selectedImage": "group_aputure80c_barndoor_p", "type": 9, "combinationType": 24, "accessoryId": "9121", "lightingId": "4290"}, {"elementId": "10507", "name": "STORM 80c", "nomalImage": "group_aputure80c_fresnel_n", "selectedImage": "group_aputure80c_fresnel_p", "type": 9, "combinationType": 24, "accessoryId": "9122", "lightingId": "4290"}, {"elementId": "10508", "name": "STORM 80c", "nomalImage": "group_aputure80c_transferbowl_n", "selectedImage": "group_aputure80c_transferbowl_p", "type": 9, "combinationType": 24, "accessoryId": "9123", "lightingId": "4290"}, {"elementId": "10509", "name": "STORM 80c", "nomalImage": "group_aputure80c_holderbowl_n", "selectedImage": "group_aputure80c_holderbowl_p", "type": 9, "combinationType": 24, "accessoryId": "9124", "lightingId": "4290"}, {"elementId": "10510", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_reflector_n", "selectedImage": "group_600cpro_reflector_p", "type": 9, "combinationType": 3, "accessoryId": "9000", "lightingId": "4291"}, {"elementId": "10511", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_domemini_n", "selectedImage": "group_600cpro_domemini_p", "type": 9, "combinationType": 3, "accessoryId": "9001", "lightingId": "4291"}, {"elementId": "10512", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_dome_n", "selectedImage": "group_600cpro_dome_p", "type": 9, "combinationType": 3, "accessoryId": "9002", "lightingId": "4291"}, {"elementId": "10513", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_fresnel_n", "selectedImage": "group_600cpro_fresnel_p", "type": 9, "combinationType": 3, "accessoryId": "9003", "lightingId": "4291"}, {"elementId": "10514", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_fresnel2x_n", "selectedImage": "group_600cpro_fresnel2x_p", "type": 9, "combinationType": 3, "accessoryId": "9004", "lightingId": "4291"}, {"elementId": "10515", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_barndoor_n", "selectedImage": "group_600cpro_barndoor_p", "type": 9, "combinationType": 3, "accessoryId": "9005", "lightingId": "4291"}, {"elementId": "10516", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_spotlight_n", "selectedImage": "group_600cpro_spotlight_p", "type": 9, "combinationType": 3, "accessoryId": "9006", "lightingId": "4291"}, {"elementId": "10517", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_dome120_n", "selectedImage": "group_600cpro_dome120_p", "type": 9, "combinationType": 3, "accessoryId": "9018", "lightingId": "4291"}, {"elementId": "10518", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_lightbox6090_n", "selectedImage": "group_600cpro_lightbox6090_p", "type": 9, "combinationType": 3, "accessoryId": "9019", "lightingId": "4291"}, {"elementId": "10519", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_lightbox6090grid_n", "selectedImage": "group_600cpro_lightbox6090grid_p", "type": 9, "combinationType": 3, "accessoryId": "9020", "lightingId": "4291"}, {"elementId": "10520", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_lightbox30120_n", "selectedImage": "group_600cpro_lightbox30120_p", "type": 9, "combinationType": 3, "accessoryId": "9021", "lightingId": "4291"}, {"elementId": "10521", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_lantern_n", "selectedImage": "group_600cpro_lantern_p", "type": 9, "combinationType": 3, "accessoryId": "9083", "lightingId": "4291"}, {"elementId": "10522", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_lantern65_n", "selectedImage": "group_600cpro_lantern65_p", "type": 9, "combinationType": 3, "accessoryId": "9084", "lightingId": "4291"}, {"elementId": "10523", "name": "LS 600c Pro Ⅱ", "nomalImage": "group_600cpro_spotlightse_n", "selectedImage": "group_600cpro_spotlightse_p", "type": 9, "combinationType": 3, "accessoryId": "9085", "lightingId": "4291"}, {"elementId": "10524", "name": "amaran Ace 25x", "nomalImage": "alight_mr044_n", "selectedImage": "alight_mr044_p", "type": 9, "combinationType": 25, "accessoryId": "9127", "lightingId": "4292"}, {"elementId": "10525", "name": "amaran Ace 25x", "nomalImage": "group_mr044_grid_n", "selectedImage": "group_mr044_grid_p", "type": 9, "combinationType": 25, "accessoryId": "9125", "lightingId": "4292"}, {"elementId": "10526", "name": "amaran Ace 25x", "nomalImage": "group_mr044_dome_n", "selectedImage": "group_mr044_dome_p", "type": 9, "combinationType": 25, "accessoryId": "9126", "lightingId": "4292"}, {"elementId": "10527", "name": "amaran Ace 25c", "nomalImage": "alight_mr044_n", "selectedImage": "alight_mr044_p", "type": 9, "combinationType": 25, "accessoryId": "9127", "lightingId": "4293"}, {"elementId": "10528", "name": "amaran Ace 25c", "nomalImage": "group_mr044_grid_n", "selectedImage": "group_mr044_grid_p", "type": 9, "combinationType": 25, "accessoryId": "9125", "lightingId": "4293"}, {"elementId": "10529", "name": "amaran Ace 25c", "nomalImage": "group_mr044_dome_n", "selectedImage": "group_mr044_dome_p", "type": 9, "combinationType": 25, "accessoryId": "9126", "lightingId": "4293"}, {"elementId": "10530", "name": "INFINIMAT 1x2", "nomalImage": "group_infinimat_1x2_inflatablesoftbox_n", "selectedImage": "group_infinimat_1x2_inflatablesoftbox_p", "type": 9, "combinationType": 26, "accessoryId": "9128", "lightingId": "4295"}, {"elementId": "10531", "name": "INFINIMAT 1x4", "nomalImage": "group_infinimat_1x4_inflatablesoftbox_n", "selectedImage": "group_infinimat_1x4_inflatablesoftbox_p", "type": 9, "combinationType": 26, "accessoryId": "9128", "lightingId": "4296"}, {"elementId": "10532", "name": "INFINIMAT 2x4", "nomalImage": "group_infinimat_2x4_inflatablesoftbox_n", "selectedImage": "group_infinimat_2x4_inflatablesoftbox_p", "type": 9, "combinationType": 26, "accessoryId": "9128", "lightingId": "4297"}, {"elementId": "10533", "name": "INFINIMAT 4x4", "nomalImage": "group_infinimat_4x4_inflatablesoftbox_n", "selectedImage": "group_infinimat_4x4_inflatablesoftbox_p", "type": 9, "combinationType": 26, "accessoryId": "9128", "lightingId": "4298"}, {"elementId": "10534", "name": "INFINIMAT 8x8", "nomalImage": "group_infinimat_8x8_inflatablesoftbox_n", "selectedImage": "group_infinimat_8x8_inflatablesoftbox_p", "type": 9, "combinationType": 26, "accessoryId": "9128", "lightingId": "4299"}, {"elementId": "10535", "name": "INFINIMAT 20x20", "nomalImage": "group_infinimat_20x20_inflatablesoftbox_n", "selectedImage": "group_infinimat_20x20_inflatablesoftbox_p", "type": 9, "combinationType": 26, "accessoryId": "9128", "lightingId": "4300"}, {"elementId": "10536", "name": "INFINIMAT 1x2", "nomalImage": "group_infinimat_1x2_grille_n", "selectedImage": "group_infinimat_1x2_grille_p", "type": 9, "combinationType": 26, "accessoryId": "9129", "lightingId": "4295"}, {"elementId": "10537", "name": "INFINIMAT 1x4", "nomalImage": "group_infinimat_1x4_grille_n", "selectedImage": "group_infinimat_1x4_grille_p", "type": 9, "combinationType": 26, "accessoryId": "9129", "lightingId": "4296"}, {"elementId": "10538", "name": "INFINIMAT 2x4", "nomalImage": "group_infinimat_2x4_grille_n", "selectedImage": "group_infinimat_2x4_grille_p", "type": 9, "combinationType": 26, "accessoryId": "9129", "lightingId": "4297"}, {"elementId": "10539", "name": "INFINIMAT 4x4", "nomalImage": "group_infinimat_4x4_grille_n", "selectedImage": "group_infinimat_4x4_grille_p", "type": 9, "combinationType": 26, "accessoryId": "9129", "lightingId": "4298"}, {"elementId": "10540", "name": "INFINIMAT 8x8", "nomalImage": "group_infinimat_8x8_grille_n", "selectedImage": "group_infinimat_8x8_grille_p", "type": 9, "combinationType": 26, "accessoryId": "9129", "lightingId": "4299"}, {"elementId": "10541", "name": "INFINIMAT 20x20", "nomalImage": "group_infinimat_20x20_grille_n", "selectedImage": "group_infinimat_20x20_grille_p", "type": 9, "combinationType": 26, "accessoryId": "9129", "lightingId": "4300"}, {"elementId": "10542", "name": "INFINIMAT 1x2", "nomalImage": "alight_infinimat_1x2_n", "selectedImage": "alight_infinimat_1x2_p", "type": 9, "combinationType": 26, "accessoryId": "9130", "lightingId": "4295"}, {"elementId": "10543", "name": "INFINIMAT 1x4", "nomalImage": "alight_infinimat_1x4_n", "selectedImage": "alight_infinimat_1x4_p", "type": 9, "combinationType": 26, "accessoryId": "9130", "lightingId": "4296"}, {"elementId": "10544", "name": "INFINIMAT 2x4", "nomalImage": "alight_infinimat_2x4_n", "selectedImage": "alight_infinimat_2x4_p", "type": 9, "combinationType": 26, "accessoryId": "9130", "lightingId": "4297"}, {"elementId": "10545", "name": "INFINIMAT 4x4", "nomalImage": "alight_infinimat_4x4_n", "selectedImage": "alight_infinimat_4x4_p", "type": 9, "combinationType": 26, "accessoryId": "9130", "lightingId": "4298"}, {"elementId": "10546", "name": "INFINIMAT 8x8", "nomalImage": "alight_infinimat_8x8_n", "selectedImage": "alight_infinimat_8x8_p", "type": 9, "combinationType": 26, "accessoryId": "9130", "lightingId": "4299"}, {"elementId": "10547", "name": "INFINIMAT 20x20", "nomalImage": "alight_infinimat_20x20_n", "selectedImage": "alight_infinimat_20x20_p", "type": 9, "combinationType": 26, "accessoryId": "9130", "lightingId": "4300"}, {"elementId": "10548", "name": "Pano 60c", "nomalImage": "alight_pano60c_n", "selectedImage": "alight_pano60c_p", "type": 9, "combinationType": 27, "accessoryId": "9131", "lightingId": "4301"}, {"elementId": "10849", "name": "Pano 60c", "nomalImage": "group_pano60c_banrndoor_n", "selectedImage": "group_pano60c_banrndoor_p", "type": 9, "combinationType": 27, "accessoryId": "9132", "lightingId": "4301"}, {"elementId": "10850", "name": "Pano 120c", "nomalImage": "alight_pano120c_n", "selectedImage": "alight_pano120c_p", "type": 9, "combinationType": 28, "accessoryId": "9133", "lightingId": "4302"}, {"elementId": "10851", "name": "Pano 120c", "nomalImage": "group_pano120c_banrndoor_n", "selectedImage": "group_pano120c_banrndoor_p", "type": 9, "combinationType": 28, "accessoryId": "9134", "lightingId": "4302"}, {"elementId": "10852", "name": "STORM XT52", "nomalImage": "alight_xt52_n", "selectedImage": "alight_xt52_p", "type": 9, "combinationType": 29, "accessoryId": "9135", "lightingId": "4303"}, {"elementId": "10853", "name": "STORM XT52", "nomalImage": "group_xt52_barndoor_n", "selectedImage": "group_xt52_barndoor_p", "type": 9, "combinationType": 29, "accessoryId": "9136", "lightingId": "4303"}, {"elementId": "10854", "name": "STORM XT52", "nomalImage": "group_xt52_electric_fresnel_n", "selectedImage": "group_xt52_electric_fresnel_p", "type": 9, "combinationType": 29, "accessoryId": "9137", "lightingId": "4303"}, {"elementId": "10855", "name": "STORM XT52", "nomalImage": "group_xt52_lantern120_n", "selectedImage": "group_xt52_lantern120_p", "type": 9, "combinationType": 29, "accessoryId": "9138", "lightingId": "4303"}, {"elementId": "10856", "name": "STORM XT52", "nomalImage": "group_xt52_dome150_n", "selectedImage": "group_xt52_dome150_p", "type": 9, "combinationType": 29, "accessoryId": "9139", "lightingId": "4303"}, {"elementId": "10857", "name": "STORM XT52", "nomalImage": "group_xt52_parallel_n", "selectedImage": "group_xt52_parallel_p", "type": 9, "combinationType": 29, "accessoryId": "9140", "lightingId": "4303"}, {"elementId": "10858", "name": "STORM XT52", "nomalImage": "group_xt52_reflector15_n", "selectedImage": "group_xt52_reflector15_p", "type": 9, "combinationType": 29, "accessoryId": "9141", "lightingId": "4303"}, {"elementId": "10859", "name": "STORM XT52", "nomalImage": "group_xt52_reflector30_n", "selectedImage": "group_xt52_reflector30_p", "type": 9, "combinationType": 29, "accessoryId": "9142", "lightingId": "4303"}, {"elementId": "10860", "name": "STORM XT52", "nomalImage": "group_xt52_reflector45_n", "selectedImage": "group_xt52_reflector45_p", "type": 9, "combinationType": 29, "accessoryId": "9143", "lightingId": "4303"}, {"elementId": "1002", "listImage": "list_wall_n", "name": "diagram_element_scene_wall", "nomalImage": "scene_wall_n", "selectedImage": "scene_wall_p", "statisticsImage": "statistics_wall_n", "type": 10}]