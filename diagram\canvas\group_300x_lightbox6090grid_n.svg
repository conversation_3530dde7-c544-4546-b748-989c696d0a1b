<?xml version="1.0" encoding="UTF-8"?>
<svg width="122px" height="177px" viewBox="0 0 122 177" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>group_300x_lightbox6090grid_n</title>
    <g id="手机端" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="LS系列组合图标示例备份-13" transform="translate(-2736.000000, -169.000000)">
            <g id="group_300x_lightbox6090grid_n" transform="translate(2736.000000, 169.000000)">
                <g id="lsc300备份-19" transform="translate(28.695576, 77.465649)">
                    <path d="M8.15763738,80.2577521 L8.1865628,26.79729 L1.4185628,26.7977099 L1.4185628,14.1119593 L8.1935628,14.11129 L8.19664382,9.0592696 L14.3035879,2.00328996 L51.5773083,2.00328996 L58.4373443,9.0592696 L58.4365628,14.11129 L65.5377806,14.1119593 L65.5377806,26.7977099 L58.4365628,26.79729 L58.4373443,90.5342715 C58.4373443,93.84798 55.7510528,96.5342715 52.4373443,96.5342715 L13.6966924,96.5342715 C10.3829839,96.5342715 7.69669236,93.84798 7.69669236,90.5342715 L7.69669236,80.2577521 L8.15763738,80.2577521 Z" id="fill" fill="#FFFFFF"></path>
                    <path d="M50.2757237,0 C50.9375521,0 51.5732601,0.259404662 52.0475938,0.723022765 L55.2245862,3.82824427 C55.5887908,4.18422119 55.8053455,4.63518093 55.8732023,5.1040842 L55.8965902,5.1043257 C58.7030297,5.1043257 60.9780984,7.38961016 60.9780984,10.2086514 L60.9780984,12.7608142 L60.9780984,12.7608142 L63.5188525,12.7608142 C64.9220723,12.7608142 66.0596066,13.9034565 66.0596066,15.3129771 L66.0596066,25.5216285 C66.0596066,26.9311491 64.9220723,28.0737913 63.5188525,28.0737913 L60.9780984,28.0737913 L60.9786703,74.3853455 C61.8913603,74.386332 62.7729452,74.882676 63.2271009,75.7505908 C63.4187271,76.1167982 63.5188525,76.5243102 63.5188525,76.9380238 L63.5188525,93.8604652 C63.5188525,94.3742226 63.2121337,94.8378776 62.7406104,95.0369035 L52.3215291,99.434708 C52.165716,99.5004754 51.9984045,99.5343511 51.8293942,99.5343511 L14.2302124,99.5343511 C14.0612021,99.5343511 13.8938907,99.5004754 13.7380776,99.434708 L3.31899626,95.0369035 C2.84747298,94.8378776 2.5407541,94.3742226 2.5407541,93.8604652 L2.5407541,76.9380238 C2.5407541,75.5285032 3.67828846,74.385861 5.0815082,74.385861 L5.0815082,28.0737913 L5.0815082,28.0737913 L2.5407541,28.0737913 C1.13753436,28.0737913 0,26.9311491 0,25.5216285 L0,15.3129771 C0,13.9034565 1.13753436,12.7608142 2.5407541,12.7608142 L5.0815082,12.7608142 L5.0815082,10.2086514 C5.0815082,7.38961016 7.35657692,5.1043257 10.1630164,5.1043257 L10.1864823,5.10426101 C10.2566133,4.62180405 10.4829074,4.1724028 10.8350204,3.82824427 L14.0120128,0.723022765 C14.4863465,0.259404662 15.1220545,0 15.7838829,0 L50.2757237,0 Z M7.61972155,81.1613308 L7.61972155,91.3138346 L8.9545082,91.8773257 L14.9904492,94.4274733 L51.0666167,94.4274733 L58.4348036,91.3138346 L58.4348036,81.163883 L55.8965082,82.5033257 L50.798835,85.1957347 L15.2607716,85.1957347 L10.1625082,82.5043257 L7.61972155,81.1613308 Z M55.8965902,10.2086514 L10.1630164,10.2086514 L10.162,76.737 L16.5149017,80.091409 L49.544705,80.091409 L55.896,76.737 L55.8965902,10.2086514 Z M5.0815082,15.3129771 L2.5407541,15.3129771 L2.5407541,25.5216285 L5.0815082,25.5216285 L5.0815082,15.3129771 Z M63.5188525,15.3129771 L60.9780984,15.3129771 L60.9780984,25.5216285 L63.5188525,25.5216285 L63.5188525,15.3129771 Z M50.2757237,2.55216285 L15.7838829,2.55216285 L13.1687285,5.1043257 L52.8883374,5.1043257 L50.2757237,2.55216285 Z" id="形状结合" fill="#000000" fill-rule="nonzero"></path>
                </g>
                <g id="lightbox_30120备份-4">
                    <path d="M33.5902638,3.07271868 L92.0276081,3.07271868 C106.945747,20.623629 115.469157,33.6112639 117.597837,42.0356234 C119.726517,50.4599829 107.380621,61.472048 80.5601484,75.0718186 L44.6906787,75.0718186 C15.8087056,60.5321884 2.58813783,49.5201233 5.0289753,42.0356234 C7.46981277,34.5511235 16.9902423,21.5634886 33.5902638,3.07271868 Z" id="fill" fill="#FFFFFF"></path>
                    <path d="M77.2190437,39.4183724 C84.3575627,39.8832504 98.1887871,44.0358545 106.756227,51.8761847 C113.390875,57.9477562 118.336099,64.5549097 121.5919,71.6976451 C122.622792,73.9592436 121.633293,76.6320829 119.3818,77.6675952 C118.796071,77.9369855 118.159428,78.076428 117.515221,78.076428 L2.98904259,78.1766222 C1.33823995,78.1766222 0,76.8322929 0,75.1740777 C0,74.7434636 0.75448577,72.0893073 0.932652994,71.6976451 C5.41034863,61.8543994 10.0434417,56.8385879 15.7321471,51.8761847 C23.548142,45.0580924 32.8374623,41.7860571 43.7506724,39.4183724 L77.2190437,39.4183724 Z" id="矩形-copy-446" fill="#000000" fill-rule="nonzero" transform="translate(61.000000, 58.797497) rotate(-180.000000) translate(-61.000000, -58.797497) "></path>
                    <path d="M89.7568216,0 L33.0122108,0 C31.1248627,0 29.3483465,0.895244461 28.2199974,2.41495266 L1.46813615,38.445487 C0.698094029,39.4826122 0.282104673,40.7418187 0.282104673,42.0356234 C0.282104673,45.3521425 2.9586561,48.0407125 6.26034962,48.0407125 L115.377871,48.0407125 C116.623875,48.0407125 117.838741,47.6496422 118.852656,46.9221654 C121.539354,44.9944778 122.16164,41.2439989 120.242569,38.5452363 L94.6215197,2.51470194 C93.4993342,0.936587822 91.6875024,0 89.7568216,0 Z M33.0122108,6.00508906 L89.7568216,6.00508906 L115.377871,42.0356234 L6.26034962,42.0356234 L33.0122108,6.00508906 Z" id="矩形" fill="#000000" fill-rule="nonzero"></path>
                    <polygon id="矩形" fill="#000000" fill-rule="nonzero" transform="translate(61.109253, 34.537006) scale(1, -1) translate(-61.109253, -34.537006) " points="113.418896 33.7863694 113.418896 35.2876416 8.79960951 35.2876416 8.79960951 33.7863694"></polygon>
                    <polygon id="矩形-copy-191" fill="#000000" fill-rule="nonzero" transform="translate(59.859900, 23.370067) scale(1, -1) translate(-59.859900, -23.370067) " points="104.696737 22.6194309 104.696737 24.1207031 15.0230632 24.1207031 15.0230632 22.6194309"></polygon>
                    <polygon id="矩形-copy-191备份-4" fill="#000000" fill-rule="nonzero" transform="translate(61.088930, 28.759504) scale(1, -1) translate(-61.088930, -28.759504) " points="110.409451 28.0088674 110.409451 29.5101397 11.7684093 29.5101397 11.7684093 28.0088674"></polygon>
                    <polygon id="矩形-copy-191备份" fill="#000000" fill-rule="nonzero" transform="translate(61.316828, 18.193765) scale(1, -1) translate(-61.316828, -18.193765) " points="104.659104 17.4431285 104.659104 18.9444007 17.9745522 18.9444007 17.9745522 17.4431285"></polygon>
                    <polygon id="矩形-copy-191备份-2" fill="#000000" fill-rule="nonzero" transform="translate(61.336350, 13.252482) scale(1, -1) translate(-61.336350, -13.252482) " points="101.689504 12.5018458 101.689504 14.0031181 20.983197 14.0031181 20.983197 12.5018458"></polygon>
                    <polygon id="矩形-copy-191备份-3" fill="#000000" fill-rule="nonzero" transform="translate(59.840171, 8.890357) scale(1, -1) translate(-59.840171, -8.890357) " points="95.709641 8.13972111 95.709641 9.64099338 23.9707017 9.64099338 23.9707017 8.13972111"></polygon>
                    <polygon id="矩形" fill="#000000" fill-rule="nonzero" transform="translate(55.403594, 22.570889) scale(1, -1) rotate(-7.000000) translate(-55.403594, -22.570889) " points="56.1508744 3.05434961 56.1508744 42.0874285 54.6563131 42.0874285 54.6563131 3.05434961"></polygon>
                    <polygon id="矩形-copy-822" fill="#000000" fill-rule="nonzero" transform="translate(62.149477, 22.577340) scale(1, -1) translate(-62.149477, -22.577340) " points="62.8967576 3.06080099 62.8967576 42.0938799 61.4021964 42.0938799 61.4021964 3.06080099"></polygon>
                    <polygon id="矩形-copy-823" fill="#000000" fill-rule="nonzero" transform="translate(68.601159, 22.431923) scale(1, -1) rotate(7.000000) translate(-68.601159, -22.431923) " points="69.3484398 2.9153836 69.3484398 41.9484625 67.8538785 41.9484625 67.8538785 2.9153836"></polygon>
                    <polygon id="矩形-copy-824" fill="#000000" fill-rule="nonzero" transform="translate(75.530423, 22.488408) scale(1, -1) rotate(13.000000) translate(-75.530423, -22.488408) " points="76.2777033 2.97186857 76.2777033 42.0049475 74.7831421 42.0049475 74.7831421 2.97186857"></polygon>
                    <polygon id="矩形-copy-825" fill="#000000" fill-rule="nonzero" transform="translate(81.861648, 22.782361) scale(1, -1) rotate(19.000000) translate(-81.861648, -22.782361) " points="82.6089284 3.2658215 82.6089284 42.2989004 81.1143672 42.2989004 81.1143672 3.2658215"></polygon>
                    <polygon id="矩形-copy-902" fill="#000000" fill-rule="nonzero" transform="translate(88.466979, 22.774729) scale(1, -1) rotate(24.000000) translate(-88.466979, -22.774729) " points="89.2142597 3.25818943 89.2142597 42.2912683 87.7196985 42.2912683 87.7196985 3.25818943"></polygon>
                    <polygon id="矩形-copy-902备份" fill="#000000" fill-rule="nonzero" transform="translate(95.347969, 22.774969) scale(1, -1) rotate(30.000000) translate(-95.347969, -22.774969) " points="96.0952499 1.75715766 96.0952499 43.7927811 94.6006887 43.7927811 94.6006887 1.75715766"></polygon>
                    <polygon id="矩形-copy-484" fill="#000000" fill-rule="nonzero" transform="translate(27.522779, 22.554318) scale(1, -1) rotate(-29.000000) translate(-27.522779, -22.554318) " points="28.2700593 0.035233714 28.2700593 45.0734017 26.775498 45.0734017 26.775498 0.035233714"></polygon>
                    <polygon id="矩形-copy-487" fill="#000000" fill-rule="nonzero" transform="translate(34.839854, 22.558562) scale(1, -1) rotate(-20.000000) translate(-34.839854, -22.558562) " points="35.5871343 3.04202246 35.5871343 42.0751013 34.0925731 42.0751013 34.0925731 3.04202246"></polygon>
                    <polygon id="矩形-copy-820" fill="#000000" fill-rule="nonzero" transform="translate(41.641725, 22.548126) scale(1, -1) rotate(-16.000000) translate(-41.641725, -22.548126) " points="42.3890051 3.03158682 42.3890051 42.0646657 40.8944439 42.0646657 40.8944439 3.03158682"></polygon>
                    <polygon id="矩形-copy-821" fill="#000000" fill-rule="nonzero" transform="translate(48.447983, 22.544327) scale(1, -1) rotate(-12.000000) translate(-48.447983, -22.544327) " points="49.195264 3.02778727 49.195264 42.0608661 47.7007028 42.0608661 47.7007028 3.02778727"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>